use aqsoc;

ALTER TABLE tbl_business_application
    ADD COLUMN is_open_network CHAR(1) COMMENT '是否开启网络（0否，1是）',
ADD COLUMN accessible_network varchar(100) COMMENT '可访问网络',
ADD COLUMN development_language varchar(50) COMMENT '开发语言',
ADD COLUMN hw_is_true_shut_down char(1) COMMENT 'HW时期是否可关停',
ADD COLUMN protection_description varchar(500) COMMENT '安全措施防护描述';

ALTER TABLE tbl_server
    ADD COLUMN host_name VARCHAR(50) COMMENT '主机名称',
ADD COLUMN is_sparing CHAR(1) COMMENT '是否热设备0=否1=是';

ALTER TABLE tbl_safety
    ADD COLUMN buy_time DATETIME COMMENT '购买时间',
ADD COLUMN m_expiration_date DATETIME COMMENT '维保到期时间',
ADD COLUMN is_sparing char(1) DEFAULT NULL COMMENT '是否热设备0=否1=是',
ADD COLUMN manage_address VARCHAR(50) DEFAULT NULL COMMENT '管理地址';

ALTER TABLE tbl_network_devices
    ADD COLUMN manage_dept_id BIGINT DEFAULT NULL COMMENT '管理部门ID',
ADD COLUMN manage_address VARCHAR(50) DEFAULT NULL COMMENT '管理地址',
ADD COLUMN interconnect_vlan VARCHAR(100) DEFAULT NULL COMMENT '互联vlan',
ADD COLUMN interconnect_manage VARCHAR(50) DEFAULT NULL COMMENT '互联地址',
ADD COLUMN switch_interconnect_manage VARCHAR(100) DEFAULT NULL COMMENT '上联核心交换机互联地址',
ADD COLUMN is_sparing char(1) DEFAULT NULL COMMENT '是否热设备0=否1=是',
ADD COLUMN usage_time datetime DEFAULT NULL COMMENT '预计投入使用时间';



-- 1. 为 external_attack_app 表添加新字段
ALTER TABLE external_attack_app 
ADD COLUMN platform_type VARCHAR(100) DEFAULT NULL COMMENT '平台类型(IOS/Android等)，关联sys_dict_data.dict_value',
ADD COLUMN server_address VARCHAR(500) DEFAULT NULL COMMENT '服务端地址(IP/URL)',
ADD COLUMN remark TEXT DEFAULT NULL COMMENT '备注';

-- 2. 为 external_attack_official_account 表添加新字段
ALTER TABLE external_attack_official_account 
ADD COLUMN account_type VARCHAR(100) DEFAULT NULL COMMENT '账号类型，关联sys_dict_data.dict_value',
ADD COLUMN link_url VARCHAR(500) DEFAULT NULL COMMENT '链接网址',
ADD COLUMN remark TEXT DEFAULT NULL COMMENT '备注';

-- 3. 为 external_attack_mini_program 表添加新字段
ALTER TABLE external_attack_mini_program 
ADD COLUMN wechat_id VARCHAR(300) DEFAULT NULL COMMENT '微信号',
ADD COLUMN program_type VARCHAR(100) DEFAULT NULL COMMENT '小程序类型，关联sys_dict_data.dict_value',
ADD COLUMN link_url VARCHAR(500) DEFAULT NULL COMMENT '链接网址',
ADD COLUMN remark TEXT DEFAULT NULL COMMENT '备注';

-- 2025-08-13 修改tbl_safety表管理地址长度
ALTER TABLE tbl_safety 
MODIFY COLUMN `manage_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '管理地址' AFTER `is_sparing`;