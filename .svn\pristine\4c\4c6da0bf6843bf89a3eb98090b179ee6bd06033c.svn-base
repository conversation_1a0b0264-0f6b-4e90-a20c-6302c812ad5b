import request from '@/utils/request'

// 查询设备接入配置列表
export function listDeviceConfig(query) {
  return request({
    url: '/api/deviceConfig/list',
    method: 'get',
    params: query
  })
}

// 查询设备接入配置详细
export function getDeviceConfig(id) {
  return request({
    url: '/api/deviceConfig/' + id,
    method: 'get'
  })
}

// 新增设备接入配置
export function addDeviceConfig(data) {
  return request({
    url: '/api/deviceConfig',
    method: 'post',
    data: data
  })
}

// 修改设备接入配置
export function updateDeviceConfig(data) {
  return request({
    url: '/api/deviceConfig',
    method: 'put',
    data: data
  })
}

// 修改设备接入配置状态
export function updateDeviceConfigStatus(data) {
  return request({
    url: '/api/deviceConfig/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除设备接入配置
export function delDeviceConfig(id) {
  return request({
    url: '/api/deviceConfig/' + id,
    method: 'delete'
  })
}
