package com.ruoyi.ffsafe.api.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.mapper.TblDeviceConfigMapper;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 设备接入配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Service
public class TblDeviceConfigServiceImpl implements ITblDeviceConfigService
{
    @Autowired
    private TblDeviceConfigMapper tblDeviceConfigMapper;

    /**
     * 查询设备接入配置
     *
     * @param id 设备接入配置主键
     * @return 设备接入配置
     */
    @Override
    public TblDeviceConfig selectTblDeviceConfigById(Long id)
    {
        return tblDeviceConfigMapper.selectTblDeviceConfigById(id);
    }

    /**
     * 批量查询设备接入配置
     *
     * @param ids 设备接入配置主键集合
     * @return 设备接入配置集合
     */
    @Override
    public List<TblDeviceConfig> selectTblDeviceConfigByIds(Long[] ids)
    {
        return tblDeviceConfigMapper.selectTblDeviceConfigByIds(ids);
    }

    /**
     * 查询设备接入配置列表
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 设备接入配置
     */
    @Override
    public List<TblDeviceConfig> selectTblDeviceConfigList(TblDeviceConfig tblDeviceConfig)
    {
        return tblDeviceConfigMapper.selectTblDeviceConfigList(tblDeviceConfig);
    }

    /**
     * 新增设备接入配置
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 结果
     */
    @Override
    public int insertTblDeviceConfig(TblDeviceConfig tblDeviceConfig)
    {
        tblDeviceConfig.setCreateTime(DateUtils.getNowDate());
        tblDeviceConfig.setUpdateTime(tblDeviceConfig.getCreateTime());
        return tblDeviceConfigMapper.insertTblDeviceConfig(tblDeviceConfig);
    }

    /**
     * 修改设备接入配置
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 结果
     */
    @Override
    public int updateTblDeviceConfig(TblDeviceConfig tblDeviceConfig)
    {
        tblDeviceConfig.setUpdateTime(DateUtils.getNowDate());
        return tblDeviceConfigMapper.updateTblDeviceConfig(tblDeviceConfig);
    }

    /**
     * 删除设备接入配置信息
     *
     * @param id 设备接入配置主键
     * @return 结果
     */
    @Override
    public int deleteTblDeviceConfigById(Long id)
    {
        return tblDeviceConfigMapper.deleteTblDeviceConfigById(id);
    }

    /**
     * 批量删除设备接入配置
     *
     * @param ids 需要删除的设备接入配置主键
     * @return 结果
     */
    @Override
    public int deleteTblDeviceConfigByIds(Long[] ids)
    {
        return tblDeviceConfigMapper.deleteTblDeviceConfigByIds(ids);
    }
}
