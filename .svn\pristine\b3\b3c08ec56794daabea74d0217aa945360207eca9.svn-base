<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.TblDeviceConfigMapper">

    <resultMap type="TblDeviceConfig" id="TblDeviceConfigResult">
        <result property="id"    column="id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceIp"    column="device_ip"    />
        <result property="deviceParams"    column="device_params"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectTblDeviceConfigVo">
        select id, device_name, device_ip, device_params, create_time, create_by, update_time, update_by, status from tbl_device_config
    </sql>

    <select id="selectTblDeviceConfigList" parameterType="TblDeviceConfig" resultMap="TblDeviceConfigResult">
        <include refid="selectTblDeviceConfigVo"/>
        <where>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceIp != null  and deviceIp != ''"> and device_ip = #{deviceIp}</if>
            <if test="status != null "> and `status` = #{status}</if>
        </where>
    </select>

    <select id="selectTblDeviceConfigById" parameterType="Long" resultMap="TblDeviceConfigResult">
        <include refid="selectTblDeviceConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectTblDeviceConfigByIds" parameterType="Long" resultMap="TblDeviceConfigResult">
        <include refid="selectTblDeviceConfigVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblDeviceConfig" parameterType="TblDeviceConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_device_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">device_name,</if>
            <if test="deviceIp != null">device_ip,</if>
            <if test="deviceParams != null">device_params,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="status != null">`status`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null">#{deviceName},</if>
            <if test="deviceIp != null">#{deviceIp},</if>
            <if test="deviceParams != null">#{deviceParams},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateTblDeviceConfig" parameterType="TblDeviceConfig">
        update tbl_device_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceIp != null">device_ip = #{deviceIp},</if>
            <if test="deviceParams != null">device_params = #{deviceParams},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">`status` = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblDeviceConfigById" parameterType="Long">
        delete from tbl_device_config where id = #{id}
    </delete>

    <delete id="deleteTblDeviceConfigByIds" parameterType="String">
        delete from tbl_device_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
