<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box" v-show="showSearch">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px"
                 label-position="right"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="设备名称" prop="deviceName">
                <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable
                          @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="设备IP" prop="deviceIp">
                <el-input v-model="queryParams.deviceIp" placeholder="请输入设备IP" clearable
                          @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询
                </el-button>
                <el-button class="btn2" size="small" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">设备接入配置</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button type="primary" size="small" @click="handleAdd" v-hasPermi="['api:deviceConfig:add']">
                  新增
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="tableContainer">
          <el-table height="100%" v-loading="loading" :data="deviceConfigList" @selection-change="handleSelectionChange"
                    @sort-change="sortChange"
          >
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="设备名称" align="center" prop="deviceName" v-if="columns[0].visible" />
            <el-table-column label="设备IP" align="center" prop="deviceIp" v-if="columns[1].visible" />
            <el-table-column label="设备接入参数" align="center" prop="deviceParams" v-if="columns[2].visible" min-width="220" />
            <el-table-column label="修改时间" align="center" prop="updateTime" v-if="columns[3].visible"
                             width="200"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status" v-if="columns[4].visible" width="160">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" active-color="#13ce66" :disabled="!checkPermi(['api:deviceConfig:edit'])" @change="changeStatus(scope.row)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" :show-overflow-tooltip="false" width="160"
                             class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleUpdate(scope.row)">详情</el-button>
                <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
                           v-hasPermi="['api:deviceConfig:edit']"
                >编辑
                </el-button>
                <el-button size="mini" type="text"
                           @click="handleDelete(scope.row)" v-hasPermi="['api:deviceConfig:remove']"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                    @pagination="getList"
        />
      </div>
    </div>
    <!-- 添加或修改事务管理对话框! -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-row>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="form.deviceName" placeholder="请输入设备名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备IP" prop="deviceIp">
              <el-input v-model="form.deviceIp" placeholder="请输入设备IP"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="接入参数" prop="deviceParams">
              <el-input v-model="form.deviceParams" type="textarea" :autosize="{ minRows: 2, maxRows: 8}" :maxlength="2000" show-word-limit placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDeviceConfig,
  getDeviceConfig,
  delDeviceConfig,
  addDeviceConfig,
  updateDeviceConfig,
  updateDeviceConfigStatus
} from '@/api/ffsafe/deviceConfig'
import { checkPermi } from '@/utils/permission'

export default {
  name: 'DeviceConfig',
  data() {
    // 自定义JSON验证函数
    const validateJson = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('JSON不能为空'))
      }

      try {
        // 1. 尝试解析JSON
        const parsed = JSON.parse(value)

        // 2. 校验是否为对象（可选）
        if (typeof parsed !== 'object' || parsed === null) {
          return callback(new Error('JSON必须是对象或数组'))
        }

        // 3. 校验空对象/空数组（可选）
        if (Array.isArray(parsed) && parsed.length === 0) {
          return callback(new Error('数组不能为空'))
        }

        if (!Array.isArray(parsed) && Object.keys(parsed).length === 0) {
          return callback(new Error('对象不能为空'))
        }

        callback()
      } catch (e) {
        // 4. 提取JSON解析错误的具体位置
        const errorPos = this.formatJsonError(e.message)
        callback(new Error(`无效的JSON格式: ${errorPos || e.message}`))
      }
    }

    return {
      //用户选择
      userDialog: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备接入配置表格数据
      deviceConfigList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        isAsc: undefined,
        orderByColumn: undefined,
        pageNum: 1,
        pageSize: 10,
        deviceName: null,
        deviceIp: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceName: [
          { required: true, message: '设备名称不能为空', trigger: 'blur' }
        ],
        deviceIp: [
          { required: true, message: '设备IP不能为空', trigger: 'blur' }
        ],
        deviceParams: [
          { required: true, message: '设备接入参数不能为空', trigger: 'blur' },
          { validator: validateJson, trigger: ['blur'] }
        ]
      },
      columns: [
        { key: 0, label: '设备名称', visible: true },
        { key: 1, label: '设备IP', visible: true },
        { key: 2, label: '设备接入参数', visible: true },
        { key: 3, label: '修改时间', visible: true },
        { key: 4, label: '状态', visible: true }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermi,
    //获取人员数据
    getTableData() {
    },
    //关闭用户窗口
    closeUserDialog() {
      this.userDialog = false
    },
    //打开用户选择窗口
    showUserDialog(val) {
      this.dialogName = val
      this.userDialog = true
    },
    //排序
    sortChange(column, prop, order) {
      if (column.order != null) {
        this.queryParams.isAsc = 'desc'
      } else {
        this.queryParams.isAsc = 'asc'
      }
      this.queryParams.orderByColumn = column.prop
      this.getList(this.queryParams)
    },
    /** 查询设备接入配置列表 */
    getList() {
      this.loading = true
      listDeviceConfig(this.queryParams).then(response => {
        this.deviceConfigList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceName: null,
        deviceIp: null,
        deviceParams: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        status: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加设备接入配置'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getDeviceConfig(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改设备接入配置'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDeviceConfig(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addDeviceConfig(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除设备接入配置编号为"' + ids + '"的数据项？').then(function() {
        return delDeviceConfig(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('api/deviceConfig/export', {
        ...this.queryParams
      }, `deviceConfig_${new Date().getTime()}.xlsx`)
    },
    changeStatus(row){
      const text = row.status === 1 ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.deviceName + '"吗？').then(() => {
        const data = {
          id: row.id,
          status: row.status
        }
        return updateDeviceConfigStatus(data)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(() => {
        row.status = row.status === 0 ? 1 : 0
      })
    },
    // 格式化JSON错误消息
    formatJsonError(message) {
      const matches = message.match(/position (\d+)/);
      if (matches && matches[1]) {
        const position = parseInt(matches[1], 10);
        return `错误位置: 第 ${position} 个字符处`;
      }
      return message.replace('JSON.parse:', '');
    },
  }
}
</script>
