package com.ruoyi.ffsafe.scantaskapi.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 批量报告生成参数类
 * 
 * 
 * @date 2025-08-15
 */
@Data
@Schema(description = "批量报告生成参数")
public class BatchCreateReportParam {

    /**
     * 扫描任务汇总ID数组
     */
    @Schema(description = "扫描任务汇总ID数组", required = true, example = "[1, 2, 3]")
    @NotEmpty(message = "任务ID不能为空")
    private Long[] ids;

    /**
     * 任务类型 2: 主机漏扫任务  1: web漏扫任务
     */
    @Schema(description = "任务类型 2: 主机漏扫任务  1: web漏扫任务", required = true, example = "2")
    @NotNull(message = "任务类型不能为空")
    private Integer taskType;

    /**
     * 报告文件名（可选，系统会自动生成）
     */
    @Schema(description = "报告文件名（可选，系统会自动生成）", example = "batch_report_20250115")
    private String fileName;

    /**
     * 备注信息（可选）
     */
    @Schema(description = "备注信息", example = "批量生成月度安全扫描报告")
    private String remark;
}
