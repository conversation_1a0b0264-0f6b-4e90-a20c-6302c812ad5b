package com.ruoyi.ffsafe.scantaskapi.domain;

import lombok.Data;
import lombok.ToString;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@ToString
@Component
public class CreateTaskReportParam  extends ParamBase implements RequestBase {
    private String taskId;    // 修改为String类型，支持单个ID或逗号分隔的多个ID
    private int reportType;   //1: 主机漏扫综合，2: 网站漏扫综合
    private String fileName;
    private String userId;

    /**
     * 单个任务的解析方法（保持向后兼容）
     * @param taskId 单个任务ID
     * @param reportType 报告类型
     * @param fileName 文件名
     */
    public void parseParam(int taskId, int reportType, String fileName) {
        this.taskId = String.valueOf(taskId);
        this.reportType = reportType;
        this.fileName = fileName;
    }

    /**
     * 批量任务的解析方法（新增）
     * @param taskIds 任务ID数组
     * @param reportType 报告类型
     * @param fileName 文件名
     */
    public void parseParam(Long[] taskIds, int reportType, String fileName) {
        this.taskId = String.join(",", Arrays.stream(taskIds)
                                            .map(String::valueOf)
                                            .toArray(String[]::new));
        this.reportType = reportType;
        this.fileName = fileName;
    }

    @Override
    public HttpRequestBase getRequestBase() {
        List<NameValuePair> params = new ArrayList<NameValuePair>();
        if (ffurl == null) {
            if (!updateFfsafeApiConfig()) {
                return null;
            }
        }

        userId = "1";
        HttpPost httpPost = new HttpPost(ffurl + "/v1/report/" + userId);
        try {
            params.add(new BasicNameValuePair("access_token", fftoken));
            params.add(new BasicNameValuePair("task_id", taskId));  // 直接传递taskId，支持单个或逗号分隔的多个
            params.add(new BasicNameValuePair("file_type", String.valueOf(reportType)));
            params.add(new BasicNameValuePair("filename", fileName));

            StringEntity entity = new UrlEncodedFormEntity(params, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return httpPost;
    }
}
