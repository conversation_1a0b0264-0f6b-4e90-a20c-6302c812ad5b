package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫描报告与任务关联对象 ffsafe_scan_report_task_relation
 * 
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FfsafeScanReportTaskRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 扫描报告记录ID */
    @Excel(name = "扫描报告记录ID")
    private Long scanReportRecordId;

    /** 扫描任务汇总ID */
    @Excel(name = "扫描任务汇总ID")
    private Integer taskSummaryId;
}
