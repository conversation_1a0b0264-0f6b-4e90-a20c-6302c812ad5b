package com.ruoyi.ffsafe.scantaskapi.event;

import com.ruoyi.common.utils.MinioUtil;
import com.ruoyi.ffsafe.scantaskapi.domain.*;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScanReportRecordService;
import com.ruoyi.ffsafe.scantaskapi.service.IScanTaskService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 批量报告监控事件处理类
 * 专门负责监控 ffsafe_scan_report_record 表中的批量报告状态
 * 
 * @date 2025-08-15
 */
@Slf4j
@Data
@Component
public class BatchReportMonitorEvent {
    
    private boolean isRunning;
    private List<FfsafeScanReportRecord> monitoringReports;
    
    @Autowired
    private IFfsafeScanReportRecordService ffsafeScanReportRecordService;
    
    @Autowired
    private IScanTaskService scanTaskService;
    
    @Autowired
    private QueryTaskReportParam queryTaskReportParam;
    
    @Autowired
    private DownTaskReportParam downTaskReportParam;
    
    @Autowired
    private MinioUtil minioUtil;

    /**
     * 添加批量报告到监控列表
     */
    public void addBatchReport(FfsafeScanReportRecord reportRecord) {
        synchronized (monitoringReports) {
            monitoringReports.add(reportRecord);
            log.info("批量报告已添加到监控，报告ID: {}, 记录ID: {}", 
                    reportRecord.getReportId(), reportRecord.getId());
        }
    }

    /**
     * 从监控列表移除批量报告
     */
    public void removeBatchReport(FfsafeScanReportRecord reportRecord) {
        synchronized (monitoringReports) {
            monitoringReports.removeIf(report -> 
                report.getId().equals(reportRecord.getId()));
            log.info("批量报告已从监控移除，报告ID: {}, 记录ID: {}", 
                    reportRecord.getReportId(), reportRecord.getId());
        }
    }

    /**
     * 克隆监控列表（用于安全遍历）
     */
    public List<FfsafeScanReportRecord> cloneBatchReports() {
        List<FfsafeScanReportRecord> tempList = new ArrayList<>();
        synchronized (monitoringReports) {
            if (monitoringReports != null) {
                tempList.addAll(monitoringReports);
            }
        }
        return tempList;
    }

    /**
     * 初始化批量报告监控
     */
    private void initBatchReportMonitor() {
        monitoringReports = new ArrayList<>();
        
        try {
            // 查询状态为生成中(0)和生成完毕(1)的批量报告
            FfsafeScanReportRecord queryRecord = new FfsafeScanReportRecord();
            
            // 生成中的报告
            queryRecord.setReportStatus(FfsafeScanReportRecord.REPORT_STATUS_GENERATING);
            List<FfsafeScanReportRecord> generatingRecords = 
                ffsafeScanReportRecordService.selectFfsafeScanReportRecordList(queryRecord);
            
            // 生成完毕的报告
            queryRecord.setReportStatus(FfsafeScanReportRecord.REPORT_STATUS_COMPLETED);
            List<FfsafeScanReportRecord> completedRecords = 
                ffsafeScanReportRecordService.selectFfsafeScanReportRecordList(queryRecord);
            
            if (generatingRecords != null) {
                monitoringReports.addAll(generatingRecords);
            }
            
            if (completedRecords != null) {
                monitoringReports.addAll(completedRecords);
            }
            
            log.info("批量报告监控初始化完成，生成中: {}, 生成完毕: {}", 
                    generatingRecords != null ? generatingRecords.size() : 0,
                    completedRecords != null ? completedRecords.size() : 0);
                    
        } catch (Exception e) {
            log.error("初始化批量报告监控失败", e);
        }
    }

    @PostConstruct
    public void init() {
        initBatchReportMonitor();
        log.info("开始批量报告监控线程!");
        isRunning = true;
        startMonitoringThread();
    }

    /**
     * 启动监控线程
     */
    private void startMonitoringThread() {
        Thread monitorThread = new Thread(new Runnable() {
            @Override
            public void run() {
                while (isRunning) {
                    try {
                        Thread.sleep(5000); // 5秒检查一次
                        
                        List<FfsafeScanReportRecord> currentReports = cloneBatchReports();
                        for (FfsafeScanReportRecord report : currentReports) {
                            processReportStatus(report);
                            Thread.sleep(2000); // 每个报告间隔2秒
                        }
                        
                        Thread.sleep(10000); // 整轮检查完毕后休息10秒
                        
                    } catch (Exception e) {
                        log.error("批量报告监控线程出错: {}", e.getMessage(), e);
                    }
                }
            }
        });
        
        monitorThread.setName("BatchReportMonitorThread");
        monitorThread.setDaemon(true);
        monitorThread.start();
    }

    /**
     * 处理报告状态
     */
    private void processReportStatus(FfsafeScanReportRecord report) {
        try {
            log.debug("检查批量报告状态，报告ID: {}, 当前状态: {}", 
                     report.getReportId(), report.getReportStatus());
            
            if (report.getReportStatus() == FfsafeScanReportRecord.REPORT_STATUS_GENERATING) {
                // 报告生成中，查询第三方接口状态
                processGeneratingReport(report);
                
            } else if (report.getReportStatus() == FfsafeScanReportRecord.REPORT_STATUS_COMPLETED) {
                // 报告生成完毕，尝试下载
                processCompletedReport(report);
            }
            
        } catch (Exception e) {
            log.error("处理批量报告状态时发生异常，报告ID: {}", report.getReportId(), e);
        }
    }

    /**
     * 处理生成中的报告
     */
    private void processGeneratingReport(FfsafeScanReportRecord report) throws Exception {
        queryTaskReportParam.parseParam(report.getReportId());
        QueryTaskReportResult queryResult = scanTaskService.queryTaskReportStatus(queryTaskReportParam);
        
        if (queryResult == null) {
            log.warn("查询批量报告状态失败，报告ID: {}", report.getReportId());
            return;
        }
        
        if (queryResult.getTaskStatus() == 1) {
            // 报告生成完毕，更新状态并尝试下载
            updateReportToCompleted(report, queryResult);
            downloadReport(report, queryResult.getFileName());
            
        } else {
            // 报告仍在生成中，更新进度
            updateReportProgress(report, queryResult);
        }
    }

    /**
     * 处理生成完毕的报告
     */
    private void processCompletedReport(FfsafeScanReportRecord report) throws Exception {
        if (report.getDownName() != null) {
            downloadReport(report, report.getDownName());
        }
    }

    /**
     * 更新报告为生成完毕状态
     */
    private void updateReportToCompleted(FfsafeScanReportRecord report, QueryTaskReportResult queryResult) {
        try {
            FfsafeScanReportRecord updateRecord = new FfsafeScanReportRecord();
            updateRecord.setId(report.getId());
            updateRecord.setReportStatus(FfsafeScanReportRecord.REPORT_STATUS_COMPLETED);
            updateRecord.setReportPercent(100);
            updateRecord.setDownName(queryResult.getFileName());
            updateRecord.setGenerateTime(new Date()); // 记录生成完成时间
            
            ffsafeScanReportRecordService.updateFfsafeScanReportRecord(updateRecord);
            
            // 更新本地缓存
            report.setReportStatus(FfsafeScanReportRecord.REPORT_STATUS_COMPLETED);
            report.setReportPercent(100);
            report.setDownName(queryResult.getFileName());
            report.setGenerateTime(new Date());
            
            log.info("批量报告生成完毕，报告ID: {}", report.getReportId());
            
        } catch (Exception e) {
            log.error("更新批量报告状态失败，报告ID: {}", report.getReportId(), e);
        }
    }

    /**
     * 更新报告进度
     */
    private void updateReportProgress(FfsafeScanReportRecord report, QueryTaskReportResult queryResult) {
        try {
            FfsafeScanReportRecord updateRecord = new FfsafeScanReportRecord();
            updateRecord.setId(report.getId());
            updateRecord.setReportPercent(queryResult.getPercentage());
            updateRecord.setDownName(queryResult.getFileName());
            
            ffsafeScanReportRecordService.updateFfsafeScanReportRecord(updateRecord);
            
            // 更新本地缓存
            report.setReportPercent(queryResult.getPercentage());
            report.setDownName(queryResult.getFileName());
            
            log.debug("批量报告进度更新，报告ID: {}, 进度: {}%", 
                     report.getReportId(), queryResult.getPercentage());
                     
        } catch (Exception e) {
            log.error("更新批量报告进度失败，报告ID: {}", report.getReportId(), e);
        }
    }

    /**
     * 下载报告
     */
    private void downloadReport(FfsafeScanReportRecord report, String fileName) {
        try {
            downTaskReportParam.parseParam(fileName);
            boolean downloadSuccess = scanTaskService.downTaskReport(
                downTaskReportParam, 
                report.getFileName() + ".zip", 
                minioUtil
            );
            
            if (downloadSuccess) {
                // 下载成功，更新为已下载状态并移除监控
                updateReportToDownloaded(report);
                removeBatchReport(report);
                
            } else {
                log.warn("批量报告下载失败，报告ID: {}", report.getReportId());
            }
            
        } catch (Exception e) {
            log.error("下载批量报告失败，报告ID: {}", report.getReportId(), e);
        }
    }

    /**
     * 更新报告为已下载状态
     */
    private void updateReportToDownloaded(FfsafeScanReportRecord report) {
        try {
            FfsafeScanReportRecord updateRecord = new FfsafeScanReportRecord();
            updateRecord.setId(report.getId());
            updateRecord.setReportStatus(FfsafeScanReportRecord.REPORT_STATUS_DOWNLOADED);
            updateRecord.setMinioPath(report.getFileName());
            
            ffsafeScanReportRecordService.updateFfsafeScanReportRecord(updateRecord);
            
            log.info("批量报告下载完成，报告ID: {}", report.getReportId());
            
        } catch (Exception e) {
            log.error("更新批量报告下载状态失败，报告ID: {}", report.getReportId(), e);
        }
    }

    /**
     * 停止监控
     */
    public void stop() {
        isRunning = false;
        log.info("批量报告监控已停止");
    }
}
