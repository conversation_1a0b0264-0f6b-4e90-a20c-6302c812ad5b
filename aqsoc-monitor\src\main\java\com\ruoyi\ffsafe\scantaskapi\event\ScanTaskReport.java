package com.ruoyi.ffsafe.scantaskapi.event;

import com.ruoyi.ffsafe.scantaskapi.domain.CreateTaskReportParam;
import com.ruoyi.ffsafe.scantaskapi.domain.CreateTaskReportResult;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecord;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScanReportRecordService;
import com.ruoyi.ffsafe.scantaskapi.service.IScanTaskService;
import com.ruoyi.ffsafe.scantaskapi.utils.ScanTargetUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.service.ISysJobService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Data
@Component
public class ScanTaskReport {
    @Autowired
    private IScanTaskService scanTaskService;
    @Autowired
    private IFfsafeScantaskSummaryService ffsafeScantaskSummaryService;
    @Autowired
    private IFfsafeScanReportRecordService ffsafeScanReportRecordService;
    @Autowired
    private ISysJobService sysJobService;
    @Autowired
    private CreateTaskReportParam createTaskReportParam;
    @Autowired
    private TaskReportMonitorEvent taskReportMonitorEvent;
    @Autowired
    private BatchReportMonitorEvent batchReportMonitorEvent;

    private FfsafeScantaskSummary getTaskSummary(int taskId, int taskType) {
        FfsafeScantaskSummary ffsafeScantaskSummary = new FfsafeScantaskSummary();
        ffsafeScantaskSummary.setTaskId(taskId);
        ffsafeScantaskSummary.setTaskType(taskType);
        List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        if ((ffsafeScantaskSummaryList != null) &&(ffsafeScantaskSummaryList.size() > 0)) {
            return ffsafeScantaskSummaryList.get(0);
        }

        return null;
    }

    public boolean createTaskReport(int taskId, int taskType, String fileName) throws Exception {
        FfsafeScantaskSummary ffsafeScantaskSummary = getTaskSummary(taskId, taskType);
        if (ffsafeScantaskSummary == null) {
            throw new Exception("当前任务不存在!");
        }
        if (ffsafeScantaskSummary.getReportId() != null) {
            if (ffsafeScantaskSummary.getReportStatus() == 0) {
                throw new Exception("当前任务报表生成中，请勿重复生成!");
            }
            if (ffsafeScantaskSummary.getReportStatus() == 1) {    // 后台已生成， 但未上传minio
                throw new Exception("当前任务报表生成中，请勿重复生成!");
            }
            if (ffsafeScantaskSummary.getReportStatus() == 2) {
                throw new Exception("当前任务报表生成完毕。 请直接下载!");
            }
        }
        int reportType = 0;
        if (taskType == 1) { reportType = 2; }
        if (taskType == 2) { reportType = 1; }

        createTaskReportParam.parseParam(taskId, reportType, fileName);
        CreateTaskReportResult createTaskReportResult = scanTaskService.createTaskReport(createTaskReportParam);
        if (createTaskReportResult == null) {
            log.warn("生成报表失败: taskId: " + taskId + " taskType: " + taskType);
            return false;
        }
        FfsafeScantaskSummary tempSummary = new FfsafeScantaskSummary();
        tempSummary.setId(ffsafeScantaskSummary.getId());
        tempSummary.setReportId(createTaskReportResult.getTaskId());
        tempSummary.setTaskType(taskType);
        tempSummary.setReportStatus(0);  // 生成中
        tempSummary.setFileName(fileName);
        int nRet = ffsafeScantaskSummaryService.updateFfsafeScantaskSummary(tempSummary);
        taskReportMonitorEvent.addTaskReport(tempSummary);
        return nRet>0;
    }

    /**
     * 批量创建任务报告
     * @param summaryIds 扫描任务汇总主键ID数组
     * @param taskType 任务类型
     * @param fileName 文件名
     * @return 是否成功
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createBatchTaskReport(Long[] summaryIds, int taskType, String fileName) throws Exception {
        try {
            log.info("开始批量创建任务报告，任务数量: {}, 任务类型: {}, 文件名: {}",
                    summaryIds != null ? summaryIds.length : 0, taskType, fileName);

            // 1. 参数验证
            if (summaryIds == null || summaryIds.length == 0) {
                throw new ServiceException("扫描任务汇总ID列表不能为空");
            }

            // 2. 批量验证任务状态并获取任务详情（一次查询，避免重复）
            List<FfsafeScantaskSummary> validatedTasks = ffsafeScantaskSummaryService.validateBatchReportGenerationAndGet(summaryIds);

            // 3. 检查是否有任务已经在生成报告（仅单条记录时检查）
            if (summaryIds.length == 1) {
                validateNoDuplicateReports(validatedTasks);
            }

            // 4. 构建批量扫描目标信息
            String batchScanTarget = buildBatchScanTarget(validatedTasks);

            // 5. 提取任务ID用于第三方接口调用
            Integer[] taskIds = extractTaskIds(validatedTasks);

            // 6. 转换报告类型（与单个任务保持一致）
            int reportType = (taskType == 1) ? 2 : 1;

            // 7. 使用现有的 CreateTaskReportParam，传入批量 taskIds
            Long[] taskIdsLong = Arrays.stream(taskIds).map(Integer::longValue).toArray(Long[]::new);
            createTaskReportParam.parseParam(taskIdsLong, reportType, fileName);

            // 8. 调用第三方接口（复用现有方法）
            CreateTaskReportResult createTaskReportResult = scanTaskService.createTaskReport(createTaskReportParam);

            if (createTaskReportResult == null) {
                log.warn("批量生成报表失败: taskIds: {}, taskType: {}", Arrays.toString(taskIds), taskType);
                throw new ServiceException("第三方接口调用失败，无法生成批量报告");
            }

            if (!"创建成功".equals(createTaskReportResult.getMessage())) {
                log.warn("批量生成报表失败: taskIds: {}, taskType: {}, 错误信息: {}",
                        Arrays.toString(taskIds), taskType, createTaskReportResult.getMessage());
                throw new ServiceException("第三方接口返回错误: " + createTaskReportResult.getMessage());
            }

            // 9. 创建批量报告记录
            FfsafeScanReportRecord batchRecord = new FfsafeScanReportRecord();
            batchRecord.setScanTarget(batchScanTarget);
            batchRecord.setGenerateSource(FfsafeScanReportRecord.GENERATE_SOURCE_BATCH);
            batchRecord.setReportType(taskType);
            batchRecord.setReportId(createTaskReportResult.getTaskId());
            batchRecord.setReportStatus(FfsafeScanReportRecord.REPORT_STATUS_GENERATING);
            batchRecord.setFileName(fileName);
            batchRecord.setRelatedTaskSummaryIds(String.join(",",
                    Arrays.stream(summaryIds).map(String::valueOf).toArray(String[]::new)));
            batchRecord.setReportPercent(0);  // 初始进度为0

            // 10. 插入批量报告记录
            int insertResult = ffsafeScanReportRecordService.insertFfsafeScanReportRecord(batchRecord);

            if (insertResult <= 0) {
                throw new ServiceException("插入批量报告记录失败");
            }

            // 10.1 插入关联关系记录（用于优化查询性能）
            insertReportTaskRelations(batchRecord.getId(), summaryIds);

            // 11. 添加到批量报告监控事件
            batchReportMonitorEvent.addBatchReport(batchRecord);

            log.info("批量创建任务报告成功，报告ID: {}, 记录ID: {}",
                    createTaskReportResult.getTaskId(), batchRecord.getId());
            return true;

        } catch (ServiceException e) {
            log.error("批量创建任务报告业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("批量创建任务报告系统异常", e);
            throw new ServiceException("批量创建任务报告失败: " + e.getMessage());
        }
    }







    /**
     * 验证没有重复的报告生成（优化版，接收任务列表）
     * @param tasks 已验证的任务列表
     * @throws ServiceException 如果有重复报告生成
     */
    private void validateNoDuplicateReports(List<FfsafeScantaskSummary> tasks) throws ServiceException {
        for (FfsafeScantaskSummary task : tasks) {
            if (task.getReportId() != null) {
                if (task.getReportStatus() == 0) {
                    throw new ServiceException("任务ID " + task.getId() + " 报表生成中，请勿重复生成!");
                }
                if (task.getReportStatus() == 1) {
                    throw new ServiceException("任务ID " + task.getId() + " 报表生成完毕，请勿重复生成!");
                }
                if (task.getReportStatus() == 2) {
                    throw new ServiceException("任务ID " + task.getId() + " 报表已下载，请勿重复生成!");
                }
            }
        }
    }

    /**
     * 构建批量扫描目标信息（优化版，接收任务列表）
     * @param tasks 已验证的任务列表
     * @return 扫描目标信息，用分号分隔
     */
    private String buildBatchScanTarget(List<FfsafeScantaskSummary> tasks) {
        List<String> targets = new ArrayList<>();
        for (FfsafeScantaskSummary task : tasks) {
            try {
                if (task.getJobId() != null) {
                    // 通过 jobId 获取 sys_job 信息
                    SysJob sysJob = sysJobService.selectJobById(task.getJobId().longValue());
                    if (sysJob != null && sysJob.getInvokeTarget() != null) {
                        // 使用工具类解析扫描目标
                        String scanTarget = ScanTargetUtils.extractScanTarget(sysJob.getInvokeTarget());
                        if (!scanTarget.isEmpty()) {
                            targets.add(scanTarget);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("获取任务 {} 的扫描目标失败: {}", task.getId(), e.getMessage());
            }
        }
        return String.join(";", targets);
    }

    /**
     * 从任务列表中提取任务ID用于第三方接口调用
     * @param tasks 任务列表
     * @return 任务ID数组
     */
    private Integer[] extractTaskIds(List<FfsafeScantaskSummary> tasks) {
        return tasks.stream()
                   .map(FfsafeScantaskSummary::getTaskId)
                   .toArray(Integer[]::new);
    }

    /**
     * 插入报告与任务的关联关系记录
     * @param reportRecordId 报告记录ID
     * @param summaryIds 任务汇总ID数组
     */
    private void insertReportTaskRelations(Long reportRecordId, Long[] summaryIds) {
        try {
            // 这里可以批量插入关联关系，提高性能
            // 由于当前项目结构，暂时使用 related_task_summary_ids 字段存储关联关系
            // 如果需要高性能查询，可以后续扩展使用关联表
            log.debug("报告关联关系已通过 related_task_summary_ids 字段存储，报告ID: {}, 关联任务数: {}",
                     reportRecordId, summaryIds.length);
        } catch (Exception e) {
            log.warn("插入报告任务关联关系时发生异常", e);
            // 不抛出异常，避免影响主流程
        }
    }

}