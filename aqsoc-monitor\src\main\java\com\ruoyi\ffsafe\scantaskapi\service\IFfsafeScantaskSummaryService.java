package com.ruoyi.ffsafe.scantaskapi.service;

import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryDetailVO;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryQueryParam;

import java.util.List;

public interface IFfsafeScantaskSummaryService {
    /**
     * 查询非凡扫描任务汇总
     *
     * @param id 非凡扫描任务汇总主键
     * @return 非凡扫描任务汇总
     */
    public FfsafeScantaskSummary selectFfsafeScantaskSummaryById(Long id);

    /**
     * 批量查询非凡扫描任务汇总
     *
     * @param ids 非凡扫描任务汇总主键集合
     * @return 非凡扫描任务汇总集合
     */
    public List<FfsafeScantaskSummary> selectFfsafeScantaskSummaryByIds(Long[] ids);

    /**
     * 查询非凡扫描任务汇总列表
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 非凡扫描任务汇总集合
     */
    public List<FfsafeScantaskSummary> selectFfsafeScantaskSummaryList(FfsafeScantaskSummary ffsafeScantaskSummary);

    /**
     * 查询主机漏扫记录详细信息列表
     * 支持任务名称和扫描目标的模糊查询，返回包含统计信息的详细数据
     *
     * @param queryParam 查询参数，包含任务名称、扫描目标等查询条件
     * @return 主机漏扫记录详细信息集合，包含任务名称、扫描目标、存活主机数量、弱口令数量等
     */
    public List<FfsafeScantaskSummaryDetailVO> selectFfsafeScantaskSummaryDetailList(FfsafeScantaskSummaryQueryParam queryParam);

    /**
     * 新增非凡扫描任务汇总
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 结果
     */
    public int insertFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary);

    public int updateFfsafeScantaskSummaryByTaskId(FfsafeScantaskSummary ffsafeScantaskSummary);

    /**
     * 修改非凡扫描任务汇总
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 结果
     */
    public int updateFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary);

    /**
     * 删除非凡扫描任务汇总信息
     *
     * @param id 非凡扫描任务汇总主键
     * @return 结果
     */
    public int deleteFfsafeScantaskSummaryById(Long id);

    /**
     * 批量删除非凡扫描任务汇总
     *
     * @param ids 需要删除的非凡扫描任务汇总主键集合
     * @return 结果
     */
    public int deleteFfsafeScantaskSummaryByIds(Long[] ids);

    /**
     * 批量验证扫描任务是否可以生成报告并返回任务详情
     * 验证条件：finish_rate=100 且 task_status=4
     * 优化版本：避免重复查询数据库
     *
     * @param ids 需要验证的扫描任务汇总主键集合
     * @return 验证通过的任务列表
     * @throws com.ruoyi.common.exception.ServiceException 如果任何一个任务不满足条件
     */
    public List<FfsafeScantaskSummary> validateBatchReportGenerationAndGet(Long[] ids);
}
