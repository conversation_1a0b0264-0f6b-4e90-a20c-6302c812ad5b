package com.ruoyi.ffsafe.scantaskapi.service.impl;

import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryDetailVO;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryQueryParam;
import com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeScantaskSummaryMapper;
import com.ruoyi.ffsafe.scantaskapi.utils.ScanTargetUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FfsafeScantaskSummaryServiceImpl implements IFfsafeScantaskSummaryService {
    private static final Logger log = LoggerFactory.getLogger(FfsafeScantaskSummaryServiceImpl.class);

    @Autowired
    private FfsafeScantaskSummaryMapper ffsafeScantaskSummaryMapper;

    /**
     * 查询非凡扫描任务汇总
     *
     * @param id 非凡扫描任务汇总主键
     * @return 非凡扫描任务汇总
     */
    @Override
    public FfsafeScantaskSummary selectFfsafeScantaskSummaryById(Long id)
    {
        return ffsafeScantaskSummaryMapper.selectFfsafeScantaskSummaryById(id);
    }

    /**
     * 批量查询非凡扫描任务汇总
     *
     * @param ids 非凡扫描任务汇总主键集合
     * @return 非凡扫描任务汇总集合
     */
    @Override
    public List<FfsafeScantaskSummary> selectFfsafeScantaskSummaryByIds(Long[] ids)
    {
        return ffsafeScantaskSummaryMapper.selectFfsafeScantaskSummaryByIds(ids);
    }

    /**
     * 查询非凡扫描任务汇总列表
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 非凡扫描任务汇总
     */
    @Override
    public List<FfsafeScantaskSummary> selectFfsafeScantaskSummaryList(FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        return ffsafeScantaskSummaryMapper.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
    }

    /**
     * 新增非凡扫描任务汇总
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 结果
     */
    @Override
    public int insertFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        return ffsafeScantaskSummaryMapper.insertFfsafeScantaskSummary(ffsafeScantaskSummary);
    }

    /**
     * 修改非凡扫描任务汇总
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 结果
     */
    @Override
    public int updateFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary)
    {
        return ffsafeScantaskSummaryMapper.updateFfsafeScantaskSummary(ffsafeScantaskSummary);
    }

    @Override
    public int updateFfsafeScantaskSummaryByTaskId(FfsafeScantaskSummary ffsafeScantaskSummary) {
        return ffsafeScantaskSummaryMapper.updateFfsafeScantaskSummaryByTaskId(ffsafeScantaskSummary);
    }

    /**
     * 删除非凡扫描任务汇总信息
     *
     * @param id 非凡扫描任务汇总主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeScantaskSummaryById(Long id)
    {
        return ffsafeScantaskSummaryMapper.deleteFfsafeScantaskSummaryById(id);
    }

    /**
     * 批量删除非凡扫描任务汇总
     *
     * @param ids 需要删除的非凡扫描任务汇总主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeScantaskSummaryByIds(Long[] ids)
    {
        return ffsafeScantaskSummaryMapper.deleteFfsafeScantaskSummaryByIds(ids);
    }

    /**
     * 查询主机漏扫记录详细信息列表
     * 支持任务名称和扫描目标的模糊查询，返回包含统计信息的详细数据
     *
     * @param queryParam 查询参数，包含任务名称、扫描目标等查询条件
     * @return 主机漏扫记录详细信息集合，包含任务名称、扫描目标、存活主机数量、弱口令数量等
     */
    @Override
    public List<FfsafeScantaskSummaryDetailVO> selectFfsafeScantaskSummaryDetailList(FfsafeScantaskSummaryQueryParam queryParam) {
        try {
            log.debug("查询主机漏扫记录详细信息，查询参数: {}", queryParam);

            // 调用Mapper查询数据
            List<FfsafeScantaskSummaryDetailVO> list = ffsafeScantaskSummaryMapper.selectFfsafeScantaskSummaryDetailList(queryParam);

            if (list != null && !list.isEmpty()) {
                // 遍历结果列表，解析扫描目标
                for (FfsafeScantaskSummaryDetailVO item : list) {
                    if (StringUtils.isNotEmpty(item.getScanTargetRaw())) {
                        // 使用工具方法解析扫描目标
                        String scanTarget = ScanTargetUtils.extractScanTarget(item.getScanTargetRaw());
                        item.setScanTarget(scanTarget);
                    }
                }
                log.debug("查询完成，返回记录数: {}", list.size());
            } else {
                log.debug("查询完成，无匹配记录");
            }

            return list;

        } catch (Exception e) {
            log.error("查询主机漏扫记录详细信息时发生异常", e);
            throw new RuntimeException("查询主机漏扫记录详细信息失败", e);
        }
    }

    /**
     * 批量验证扫描任务是否可以生成报告并返回任务详情
     * 优化版本：避免重复查询数据库
     */
    @Override
    public List<FfsafeScantaskSummary> validateBatchReportGenerationAndGet(Long[] ids) {
        try {
            log.info("开始批量验证扫描任务报告生成条件并获取详情，任务数量: {}", ids != null ? ids.length : 0);

            if (ids == null || ids.length == 0) {
                throw new ServiceException("任务ID列表不能为空");
            }

            // 使用批量查询避免循环查询数据库
            List<FfsafeScantaskSummary> tasks = ffsafeScantaskSummaryMapper.selectFfsafeScantaskSummaryByIds(ids);

            if (tasks == null || tasks.size() != ids.length) {
                log.warn("查询到的任务数量({})与请求的任务数量({})不匹配",
                        tasks != null ? tasks.size() : 0, ids.length);
                throw new ServiceException("部分任务不存在，请检查任务ID");
            }

            // 验证每个任务的状态
            for (FfsafeScantaskSummary task : tasks) {
                // 验证完成率必须为100%
                if (task.getFinishRate() == null || !task.getFinishRate().equals(100)) {
                    log.warn("任务ID: {} 完成率不为100%，当前完成率: {}%",
                            task.getId(), task.getFinishRate());
                    throw new ServiceException("任务ID " + task.getId() + " 完成率不为100%，无法生成报告");
                }

                // 验证任务状态必须为已完成(4)
                if (task.getTaskStatus() == null || !task.getTaskStatus().equals(4)) {
                    log.warn("任务ID: {} 状态不为已完成，当前状态: {}",
                            task.getId(), task.getTaskStatus());
                    throw new ServiceException("任务ID " + task.getId() + " 状态不为已完成，无法生成报告");
                }

                log.debug("任务ID: {} 验证通过，完成率: {}%, 状态: {}",
                        task.getId(), task.getFinishRate(), task.getTaskStatus());
            }

            log.info("批量验证完成，所有任务均满足报告生成条件，返回任务详情");
            return tasks;

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("批量验证扫描任务时发生异常", e);
            throw new ServiceException("批量验证扫描任务失败: " + e.getMessage());
        }
    }
}
