package com.ruoyi.ffsafe.scantaskapi.utils;

import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 扫描目标解析工具类
 * 用于解析sys_job.invoke_target字段，提取实际的扫描目标地址
 * 
 * @date 2025-01-14
 */
public class ScanTargetUtils {
    
    private static final Logger log = LoggerFactory.getLogger(ScanTargetUtils.class);
    
    /**
     * 提取扫描目标地址
     * 
     * @param invokeTarget invoke_target字段内容
     * @return 扫描目标地址，解析失败时返回空字符串
     */
    public static String extractScanTarget(String invokeTarget) {
        if (StringUtils.isEmpty(invokeTarget)) {
            return "";
        }
        
        try {
            // 匹配最后一个单引号内的参数部分
            // 格式：MethodName.method('id','参数1|参数2|参数3')
            Pattern pattern = Pattern.compile("'([^']*)'\\s*\\)\\s*$");
            Matcher matcher = pattern.matcher(invokeTarget);
            
            if (matcher.find()) {
                String params = matcher.group(1);
                if (StringUtils.isNotEmpty(params)) {
                    // 按 | 分割参数
                    String[] parts = params.split("\\|");
                    if (parts.length > 1) {
                        // 第二个参数通常是扫描目标
                        String target = parts[1].trim();
                        if (StringUtils.isNotEmpty(target)) {
                            return target;
                        }
                    }
                }
            }
            
            log.debug("无法解析扫描目标，invoke_target: {}", invokeTarget);
            return "";
            
        } catch (Exception e) {
            log.error("解析扫描目标时发生异常，invoke_target: {}", invokeTarget, e);
            return "";
        }
    }
    
    /**
     * 判断是否为主机扫描任务
     * 
     * @param invokeTarget invoke_target字段内容
     * @return true-主机扫描，false-其他类型
     */
    public static boolean isHostScan(String invokeTarget) {
        if (StringUtils.isEmpty(invokeTarget)) {
            return false;
        }
        return invokeTarget.contains("HostVulnScan") || invokeTarget.contains("BaseServerVulnScan");
    }
    
    /**
     * 判断是否为Web扫描任务
     * 
     * @param invokeTarget invoke_target字段内容
     * @return true-Web扫描，false-其他类型
     */
    public static boolean isWebScan(String invokeTarget) {
        if (StringUtils.isEmpty(invokeTarget)) {
            return false;
        }
        return invokeTarget.contains("WebVulnScan");
    }
}
