package com.ruoyi.safe.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.safe.domain.TblSafety;
import com.ruoyi.safe.domain.TblServer;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.safe.domain.dto.QueryDeptSafetyCountDto;

import java.util.HashMap;
import java.util.List;

/**
 * 安全设备Service接口
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface ITblSafetyService
{
    /**
     * 查询安全设备
     *
     * @param assetId 安全设备主键
     * @return 安全设备
     */
    public TblSafety selectTblSafetyByAssetId(Long assetId);

    /**
     * 查询安全设备列表
     *
     * @param assetIds 安全设备主键
     * @return 安全设备集合
     */
    public List<TblSafety> selectTblSafetyByAssetIds(Long[] assetIds);

    /**
     * 查询安全设备列表
     *
     * @param tblSafety 安全设备
     * @return 安全设备集合
     */
    public List<TblSafety> selectTblSafetyList(TblSafety tblSafety);

    /**
     * 新增安全设备
     *
     * @param tblSafety 安全设备
     * @return 结果
     */
    public int insertTblSafety(TblSafety tblSafety);

    /**
     * 修改安全设备
     *
     * @param tblSafety 安全设备
     * @return 结果
     */
    public int updateTblSafety(TblSafety tblSafety);

    /**
     * 批量删除安全设备
     *
     * @param assetIds 需要删除的安全设备主键集合
     * @return 结果
     */
    public int deleteTblSafetyByAssetIds(Long[] assetIds);

    /**
     * 删除安全设备信息
     *
     * @param assetId 安全设备主键
     * @return 结果
     */
    public int deleteTblSafetyByAssetId(Long assetId);


    /**
     * 资产选择通用组件查询
     */
    List<TblSafety> assetSelectBySafety(HashMap<String,String> params);
    List<TblSafety> assetSelectBySafety2(HashMap<String,String> params);

    List<JSONObject> selectTblSafetyLocationIdIsNotNull();

    List<TblSafety> selectFirewallListByIps(List<String> firewallIps);

    int countNum();

    /**
     * 获取部门安全设备统计
     * 
     * @param queryCountDto 查询参数
     * @return 部门树结构列表
     */
    List<TreeSelect> getDeptSafetyCount(QueryDeptSafetyCountDto queryCountDto);
}
