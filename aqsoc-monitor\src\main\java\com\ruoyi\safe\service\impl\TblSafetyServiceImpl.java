package com.ruoyi.safe.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.safe.domain.MonitorHandle;
import com.ruoyi.safe.domain.TblAssetOverview;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.domain.TblSafety;
import com.ruoyi.safe.domain.dto.QueryDeptSafetyCountDto;
import com.ruoyi.safe.mapper.MonitorHandleMapper;
import com.ruoyi.safe.mapper.TblSafetyMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.safe.task.AssetOnlineTask;
import com.ruoyi.safe.vo.DeptSafetyCount;
import com.ruoyi.system.service.ISysDeptService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 安全设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@Service
public class TblSafetyServiceImpl implements ITblSafetyService
{
    @Autowired
    private TblSafetyMapper tblSafetyMapper;
    @Autowired
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private ITblNetworkIpMacService tblNetworkIpMacService;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private MonitorHandleMapper monitorHandleMapper;
    @Autowired
    private ITblFirewallPolicyService firewallPolicyService;
    @Autowired
    private ITblFirewallNatService firewallNatService;
    @Autowired
    private AssetOnlineTask assetOnlineTask;
    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询安全设备
     *
     * @param assetId 安全设备主键
     * @return 安全设备
     */
    @Override
    public TblSafety selectTblSafetyByAssetId(Long assetId)
    {
        TblSafety tblSafety = tblSafetyMapper.selectTblSafetyByAssetId(assetId);
        if(ObjectUtils.isNotEmpty(tblSafety)){
            TblAssetOverview tblAssetOverview = tblAssetOverviewService.selectInfoByAssetId(assetId);
            TblNetworkIpMac net = new TblNetworkIpMac();
            net.setMainIp("1");
            net.setAssetId(assetId);
            List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(net);
            if (tblSafety != null && tblAssetOverview != null) {
                BeanUtil.copyPropertiesIgnoreNull(tblAssetOverview, tblSafety);
                if (ObjectUtils.isNotEmpty(tblNetworkIpMacs)) {
                    tblSafety.setIp(tblNetworkIpMacs.get(0).getIpv4());
                    tblSafety.setMac(tblNetworkIpMacs.get(0).getMac());
                    tblSafety.setDomainId(tblNetworkIpMacs.get(0).getDomainId());
                    tblSafety.setDomainName(tblNetworkIpMacs.get(0).getDomainName());
                }
            }
            if(ObjectUtils.isNotEmpty(tblAssetOverview)){
                NetworkDomain tblDomain = networkDomainService.selectNetworkDomainByDomainId(tblAssetOverview.getDomainId());
                if (ObjectUtils.isNotEmpty(tblDomain)) {
                    tblSafety.setDomainName(tblDomain.getDomainName());
                }
            }
        }


        return tblSafety;
    }

    /**
     * 查询安全设备列表
     *
     * @param assetIds 安全设备主键
     * @return 安全设备集合
     */
    @Override
    public List<TblSafety> selectTblSafetyByAssetIds(Long[] assetIds)
    {
        List<TblSafety> assetList = tblSafetyMapper.selectTblSafetyByAssetIds(assetIds);
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewService.selectInfoByAssetIds(assetIds);
        CollectionUtils.associationOneToOne(
                assetList,
                tblAssetOverviews,
                TblSafety::getAssetId,
                TblAssetOverview::getAssetId,
                (asset, assetOverview) -> {
                    BeanUtil.copyPropertiesIgnoreNull(assetOverview, asset);
                }
        );
        Optional.ofNullable(assetList.stream().map(asset -> asset.getDomainId()).toArray(Long[]::new))
                .map(ids -> {
                    if (ObjectUtils.isNotEmpty(ids))
                        return networkDomainService.selectNetworkDomainByDomainIds(ids, new NetworkDomain());
                    return null;
                })
                .ifPresent(domainList -> {
                    CollectionUtils.associationOneToOne(
                            assetList, domainList,
                            TblSafety::getDomainId, NetworkDomain::getDomainId,
                            (asset, domain) -> {
                                asset.setDomainName(domain.getDomainName());
                            }
                    );
                });
        return assetList;
    }

    /**
     * 查询安全设备列表
     *
     * @param tblSafety 安全设备
     * @return 安全设备
     */
    @Override
    public List<TblSafety> selectTblSafetyList(TblSafety tblSafety)
    {
        List<TblSafety> assetList = tblSafetyMapper.selectTblSafetyList(tblSafety);

        return assetList;
    }

    /**
     * 新增安全设备
     *
     * @param tblSafety 安全设备
     * @return 结果
     */
    @Override
    public int insertTblSafety(TblSafety tblSafety)
    {
        if (tblSafety.getHandleId()!=null){
            monitorHandleMapper.deleteMonitorHandleById(tblSafety.getHandleId());
        }
        tblSafety.setAssetId(snowflake.nextId());
        tblSafety.setCreateTime(DateUtils.getNowDate());
        if(ObjectUtils.isEmpty(tblSafety.getAssetClass())){
            tblSafety.setAssetClass(3L);
        }
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(tblSafety.getAssetId());
        tblNetworkIpMac.setMainIp("1");
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if(ObjectUtils.isNotEmpty(tblSafety.getIp())||ObjectUtils.isNotEmpty(tblSafety.getDomainId())){
            if(ObjectUtils.isNotEmpty(tblNetworkIpMacs)){
                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
                mainIp.setIpv4(tblSafety.getIp());
                mainIp.setDomainId(tblSafety.getDomainId());
                mainIp.setMac(tblSafety.getMac());
                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
            }else{
                tblNetworkIpMac.setIpv4(tblSafety.getIp());
                tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
                tblNetworkIpMac.setMac(tblSafety.getMac());
                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
            }
        }else{
            tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
        }
        tblAssetOverviewService.insertTblAssetOverview(tblSafety);
        int i = tblSafetyMapper.insertTblSafety(tblSafety);
        //在线检测
        if(StrUtil.isNotBlank(tblSafety.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblSafety.getIp());
            assetInfo.put("assetId",tblSafety.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    /**
     * 修改安全设备
     *
     * @param tblSafety 安全设备
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTblSafety(TblSafety tblSafety)
    {
        TblSafety old = selectTblSafetyByAssetId(tblSafety.getAssetId());
        if (tblSafety.getHandleId()!=null){
            MonitorHandle monitorHandle=new MonitorHandle();
            monitorHandle.setId(tblSafety.getHandleId());
            monitorHandle.setIsDel(1);
            monitorHandleMapper.updateMonitorHandle(monitorHandle);
        }
        tblSafety.setUpdateTime(DateUtils.getNowDate());
        if(ObjectUtils.isEmpty(tblSafety.getAssetClass())){
            tblSafety.setAssetClass(3L);
        }
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(tblSafety.getAssetId());
        tblNetworkIpMac.setMainIp("1");
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if(ObjectUtils.isNotEmpty(tblSafety.getIp())||ObjectUtils.isNotEmpty(tblSafety.getDomainId())){
            if(ObjectUtils.isNotEmpty(tblNetworkIpMacs)){
                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
                mainIp.setIpv4(tblSafety.getIp());
                mainIp.setDomainId(tblSafety.getDomainId());
                mainIp.setMac(tblSafety.getMac());
                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
            }else{
                tblNetworkIpMac.setIpv4(tblSafety.getIp());
                tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
                tblNetworkIpMac.setMac(tblSafety.getMac());
                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
            }
        }else{
            tblNetworkIpMac.setDomainId(tblSafety.getDomainId());
            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
        }
        tblAssetOverviewService.updateTblAssetOverview(tblSafety);
        int i = tblSafetyMapper.updateTblSafety(tblSafety);
        if(old != null){
            String assetTypeDesc = tblSafety.getAssetTypeDesc();
            if(StrUtil.isNotBlank(assetTypeDesc) && !Objects.equals(tblSafety.getAssetType(), old.getAssetType()) && !assetTypeDesc.contains("防火墙")){
                //清空策略、暴露面
                firewallPolicyService.deleteTblFirewallPolicyByAssetIds(Collections.singletonList(tblSafety.getAssetId()));
                firewallNatService.deleteTblFirewallNatByAssetIds(Collections.singletonList(tblSafety.getAssetId()));
            }
        }
        //在线检测
        if(StrUtil.isNotBlank(tblSafety.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblSafety.getIp());
            assetInfo.put("assetId",tblSafety.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    /**
     * 批量删除安全设备
     *
     * @param assetIds 需要删除的安全设备主键
     * @return 结果
     */
    @Override
    public int deleteTblSafetyByAssetIds(Long[] assetIds)
    {
        tblAssetOverviewService.deleteTblAssetOverviewByIds(assetIds);
        return tblSafetyMapper.deleteTblSafetyByAssetIds(assetIds);
    }

    /**
     * 删除安全设备信息
     *
     * @param assetId 安全设备主键
     * @return 结果
     */
    @Override
    public int deleteTblSafetyByAssetId(Long assetId)
    {
        tblAssetOverviewService.deleteTblAssetOverviewById(assetId);
        return tblSafetyMapper.deleteTblSafetyByAssetId(assetId);
    }

    /**
     * 资产选择通用组件查询
     */
    @Override
    public List<TblSafety> assetSelectBySafety(HashMap<String, String> params) {
        List<TblSafety> tblSafetys = tblSafetyMapper.assetSelectBySafety(params);
        tblSafetys.forEach(server -> server.setAssetType(2L));
        return tblSafetys;
    }
    @Override
    public List<TblSafety> assetSelectBySafety2(HashMap<String, String> params) {
        List<TblSafety> tblSafetys = tblSafetyMapper.assetSelectBySafety2(params);
        tblSafetys.forEach(server -> server.setAssetType(2L));
        return tblSafetys;
    }

    @Override
    public List<JSONObject> selectTblSafetyLocationIdIsNotNull() {
        return tblSafetyMapper.selectTblSafetyLocationIdIsNotNull();
    }

    @Override
    public List<TblSafety> selectFirewallListByIps(List<String> firewallIps) {
        return tblSafetyMapper.selectFirewallListByIps(firewallIps);
    }

    @Override
    public int countNum() {
        return tblSafetyMapper.countNum();
    }

    @Override
    public List<TreeSelect> getDeptSafetyCount(QueryDeptSafetyCountDto queryCountDto) {
        queryCountDto.getSysDept().setDeptId(null);
        // 获取部门树列表
        List<TreeSelect> treeSelects = deptService.selectDeptTreeList(queryCountDto.getSysDept());
        // 获取部门ID列表
        List<Long> deptIdList = getDeptIdList(treeSelects);
        if (CollUtil.isNotEmpty(deptIdList)) {
            queryCountDto.setDeptIdList(deptIdList);
            List<DeptSafetyCount> deptSafetyCounts = tblSafetyMapper.getDeptSafetyCount(queryCountDto);
            // 创建部门ID与安全设备计数的映射
            Map<Long, DeptSafetyCount> safetyCountMap = deptSafetyCounts.stream()
                    .collect(Collectors.toMap(DeptSafetyCount::getDeptId, Function.identity()));
            // 计算祖先安全设备计数
            Map<Long, DeptSafetyCount> updatedSafetyCountMap = calculateAncestorSafetyCounts(safetyCountMap);
            // 应用并排序安全设备计数
            applyAndSortSafetyCounts(treeSelects, updatedSafetyCountMap);
        }
        return treeSelects;
    }

    /**
     * 计算祖先安全设备计数
     *
     * @param safetyCountMap
     * @return
     */
    private Map<Long, DeptSafetyCount> calculateAncestorSafetyCounts(Map<Long, DeptSafetyCount> safetyCountMap) {
        Map<Long, Integer> ancestorCounts = new HashMap<>();
        // Step 1: 计算每个祖先的安全设备计数总和
        for (DeptSafetyCount dept : safetyCountMap.values()) {
            String[] ancestors = dept.getAncestors().split(",");
            for (String ancestorId : ancestors) {
                if (NumberUtil.isLong(ancestorId)) {
                    ancestorCounts.merge(Long.parseLong(ancestorId), dept.getSafetyCount(), Integer::sum);
                }
            }
        }
        // Step 2: 使用为每个祖先计算的总和更新安全设备计数
        return safetyCountMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> new DeptSafetyCount(entry.getKey(), entry.getValue().getAncestors(),
                                ancestorCounts.getOrDefault(entry.getKey(), 0) + entry.getValue().getSafetyCount())));
    }

    /**
     * 应用并排序安全设备计数
     */
    private void applyAndSortSafetyCounts(List<TreeSelect> treeSelects, Map<Long, DeptSafetyCount> safetyCountMap) {
        for (TreeSelect treeSelect : treeSelects) {
            DeptSafetyCount safetyCount = safetyCountMap.get(treeSelect.getId());
            if (safetyCount != null) {
                int count = safetyCount.getSafetyCount() != null ? safetyCount.getSafetyCount() : 0;
                treeSelect.setCount(count);
            }
            List<TreeSelect> children = treeSelect.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                // 按安全设备计数降序对子项进行排序
                children.sort(Comparator.comparing((TreeSelect child) -> {
                    DeptSafetyCount count = safetyCountMap.getOrDefault(child.getId(), new DeptSafetyCount(child.getId(), "", 0));
                    return count.getSafetyCount();
                }).reversed().thenComparing(TreeSelect::getId));
                // 递归处理子项
                applyAndSortSafetyCounts(children, safetyCountMap);
            }
        }
    }

    /**
     * 获取部门ID列表
     *
     * @param treeSelects
     * @return
     */
    private List<Long> getDeptIdList(List<TreeSelect> treeSelects) {
        List<Long> deptIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(treeSelects)) {
            recursiveGetDeptIds(treeSelects, deptIds);
        }
        return deptIds;
    }

    /**
     * 递归获取部门ID列表
     *
     * @param nodes
     * @param deptIds
     */
    private void recursiveGetDeptIds(List<TreeSelect> nodes, List<Long> deptIds) {
        for (TreeSelect node : nodes) {
            deptIds.add(node.getId());
            if (CollUtil.isNotEmpty(node.getChildren())) {
                recursiveGetDeptIds(node.getChildren(), deptIds);
            }
        }
    }
}
