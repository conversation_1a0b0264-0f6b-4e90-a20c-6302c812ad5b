{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\ffsafe\\deviceConfig.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\ffsafe\\deviceConfig.js", "mtime": 1755138396947}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDeviceConfig", "query", "request", "url", "method", "params", "getDeviceConfig", "id", "addDeviceConfig", "data", "updateDeviceConfig", "updateDeviceConfigStatus", "delDeviceConfig"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/api/ffsafe/deviceConfig.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询设备接入配置列表\r\nexport function listDeviceConfig(query) {\r\n  return request({\r\n    url: '/api/deviceConfig/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询设备接入配置详细\r\nexport function getDeviceConfig(id) {\r\n  return request({\r\n    url: '/api/deviceConfig/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增设备接入配置\r\nexport function addDeviceConfig(data) {\r\n  return request({\r\n    url: '/api/deviceConfig',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改设备接入配置\r\nexport function updateDeviceConfig(data) {\r\n  return request({\r\n    url: '/api/deviceConfig',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改设备接入配置状态\r\nexport function updateDeviceConfigStatus(data) {\r\n  return request({\r\n    url: '/api/deviceConfig/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除设备接入配置\r\nexport function delDeviceConfig(id) {\r\n  return request({\r\n    url: '/api/deviceConfig/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACD,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,wBAAwBA,CAACF,IAAI,EAAE;EAC7C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,eAAeA,CAACL,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}