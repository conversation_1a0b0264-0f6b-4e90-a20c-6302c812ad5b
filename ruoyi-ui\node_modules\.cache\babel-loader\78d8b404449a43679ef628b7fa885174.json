{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue", "mtime": 1755138396954}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_application", "require", "_applicationLink", "_interopRequireDefault", "_applicationSite", "_userSelect", "_deptSelect", "_networkSelect", "_DynamicTag", "_vendorSelect", "_dictSelect", "_utils", "_ruoyi", "_vendor", "_serverEV", "_dateEV", "_networkEV", "_safeEV", "_overViewSelect", "_overview", "_editServer", "_user", "_domain", "_dayjs", "name", "components", "EditServer", "overViewSelect", "safeEV", "networkEV", "dateEV", "serverEV", "ApplicationLink", "ApplicationSite", "UserSelect", "DeptSelect", "NetworkSelect", "DictSelect", "DynamicTag", "VendorSelect2", "dicts", "inject", "$editable", "default", "value", "props", "assetId", "type", "String", "Number", "required", "changeId", "Function", "readonly", "Boolean", "disabled", "assetList", "Array", "data", "loading", "collapseNames", "vendorsdata", "userdata", "functionStateList", "form", "businessForm", "delList", "deptOptions", "gv", "getValFromObject", "deployLocation", "localStorage", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "managePlaceholder", "<PERSON><PERSON><PERSON>", "networkDomainOptions", "vendorsData", "refs", "collapse", "showAddServer", "serverOptions", "currentAssociationServer", "afterInit", "uploadType", "selectType", "mounted", "_this", "getAllServerList", "getDeptTree", "getManagerList", "getNetworkDomainTree", "getVendorsData", "$nextTick", "reset", "init", "computed", "getServerName", "_this2", "id", "_this2$serverOptions$", "find", "s", "assetName", "getServerIp", "_this3", "_this3$serverOptions$", "ip", "processedManagerList", "_this4", "ids", "_toConsumableArray2", "Set", "manager", "split", "filter", "map", "user", "u", "userId", "nick<PERSON><PERSON>", "phone", "phonenumber", "processedVendorsList", "_this5", "vendors", "vendorName", "processedServiceGroups", "_this6", "serviceGroup", "val", "_this6$dict$type$serv", "dict", "d", "label", "activated", "_this7", "watch", "handler", "newVal", "oldVal", "length", "for<PERSON>ach", "item", "index", "Object", "keys", "tempId", "generateSecureUUID", "systemType", "toString", "methods", "getFieldValue", "field", "_this8", "filterArr", "includes", "<PERSON><PERSON><PERSON>", "_this$dict$type$hw_is", "hw_is_true_shut_down", "dayjs", "format", "getFieldSpan", "fullSpanFields", "shouldShowField", "isadapt", "iscipher", "getDictOptions", "dictMap", "construct", "loginType", "technical", "deploy", "state", "protectGrade", "evaluationResults", "evaluationStatus", "hwIsTrueShutDown", "coverArea", "_this9", "listAllOverview", "then", "res", "_this10", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getApplication", "response", "applicationVO", "waitForValue", "$refs", "site", "getList", "tblBusinessApplication", "userNums", "everydayVisitNums", "everydayActiveNums", "tblMapperList", "i", "push", "finally", "stop", "undefined", "assetCode", "softwareVersion", "degreeImportance", "domainUrl", "assetType", "assetTypeDesc", "assetClass", "assetClassDesc", "netType", "appType", "frequency", "usageCount", "userScale", "userObject", "url", "ipd", "storage", "netenv", "iskey", "datanum", "isbase", "islink", "ishare", "islog", "isplan", "adaptDate", "cipherDate", "function", "remark", "deptId", "orgnId", "upTime", "dwid", "contactor", "domainId", "netScale", "netTopo", "netMemo", "tags", "links", "eids", "sysBusinessState", "resetForm", "serverSelect", "$set", "serverId", "_this11", "getAllDeptTree", "flattenTreeToArray", "deptTreeSelect", "_this12", "listUser", "isAsc", "orderByColumn", "isAllData", "userName", "rows", "_this13", "listDomain", "_this14", "listVendorByApplication", "applicationId", "applicationCode", "pageNum", "pageSize", "vendorCode", "vendorManageName"], "sources": ["src/views/hhlCode/component/OperationSystemDetails.vue"], "sourcesContent": ["<!--业务系统详情-->\n<template>\n  <div class=\"customForm-container\" style=\"height: 65vh\">\n    <template v-for=\"group in assetList\">\n      <div :key=\"group.formName\" style=\"margin-bottom: 20px;\">\n        <div class=\"my-title\">\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\">\n          <img v-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '用户规模'\" src=\"@/assets/images/application/yhgm.png\" alt=\"\">\n          <img v-if=\"group.formName === '业务描述'\" src=\"@/assets/images/application/ywms.png\" alt=\"\">\n          <img v-if=\"group.formName === '功能模块'\" src=\"@/assets/images/application/gnmk.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\">\n          {{ group.formName }}\n        </div>\n        <template v-if=\"group.formName === '外部连接信息'\">\n          <ApplicationLink\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            v-model=\"form.links\"/>\n        </template>\n        <template v-else-if=\"group.formName === '运营维护情况'\">\n          <ApplicationSite\n            ref=\"site\"\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            :value.sync=\"form.eids\"\n            :asset-id=\"form.assetId\"\n            multiple/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装服务器环境'\">\n          <serverEV\n            class=\"my-form\"\n            ref=\"serverEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"\n            :data-list=\"currentAssociationServer\"\n            @selected=\"serverSelect\"\n            v-if=\"afterInit\"/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装数据库环境'\">\n          <dateEV\n            class=\"my-form\"\n            ref=\"dateEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联网络设备'\">\n          <network-e-v\n            class=\"my-form\"\n            ref=\"networkEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联安全设备'\">\n          <safeEV\n            class=\"my-form\"\n            ref=\"safeEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '功能模块'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item :span=\"3\" v-for=\"(item, index) in functionStateList\" :key=\"index\">\n              <div style=\"display: flex; justify-content: space-around\">\n                <div>{{ item.moduleName }}</div>\n                <div>{{ item.moduleDesc }}</div>\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '用户规模'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === businessForm[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n              <template v-else-if=\"field.fieldKey === 'serviceGroup'\">\n                <div class=\"tag-group\">\n                  <template v-if=\"processedServiceGroups.length > 0\">\n                    <span v-for=\"(label, index) in processedServiceGroups\" :key=\"index\">\n                      {{ label }}\n                    </span>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择</span>\n                </div>\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '业务描述'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"businessForm[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              v-if=\"shouldShowField(field)\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n\n              <!-- 上传类型字段 -->\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n\n              <!-- 特殊字段：关联服务器 -->\n              <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                <div class=\"server-display\">\n                  <div v-for=\"id in form.associationServer\" :key=\"id\" class=\"server-item\">\n                    <span>{{ getServerName(id) }}</span>\n                  </div>\n                </div>\n              </template>\n\n              <!-- 特殊字段：责任人 -->\n              <template v-else-if=\"field.fieldKey === 'manager'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedManagerList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedManagerList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}（{{ user.phone }}）\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择责任人</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：单位 -->\n              <template v-else-if=\"field.fieldKey === 'deptId'\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}</span>\n              </template>\n\n              <!-- 特殊字段：主部署网络 -->\n              <template v-else-if=\"field.fieldKey === 'domainId'\">\n                <span\n                  v-for=\"(item, index) in networkDomainOptions\"\n                  :key=\"item.domainId\"\n                  v-if=\"item.domainId === form.domainId\"\n                >{{ item.domainName }}</span>\n              </template>\n\n              <!-- 特殊字段：开发合作企业 -->\n              <template v-else-if=\"field.fieldKey === 'vendor'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedVendorsList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedVendorsList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择开发合作企业</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：标签 -->\n              <template v-else-if=\"field.fieldKey === 'tags'\">\n                <template v-if=\"(form.tags || '').split(',').filter(t => t).length > 0\">\n                  <el-tag\n                    v-for=\"(tag,index) in (form.tags || '').split(',')\"\n                    :key=\"index\"\n                    closable\n                    size=\"small\"\n                    v-show=\"tag\"\n                  >\n                    {{ tag }}\n                  </el-tag>\n                </template>\n                <span v-else class=\"gray-text\">暂无标签</span>\n              </template>\n\n              <!-- 下拉选择类型字段 -->\n              <template v-else-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === form[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n\n              <!-- 默认文本显示 -->\n              <template v-else>\n                <span>{{ getFieldValue(field) }}</span>\n              </template>\n            </el-descriptions-item>\n\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {flattenTreeData, flattenTreeToArray, getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\nimport {getAllDeptTree, deptTreeSelect, listUser} from \"@/api/system/user\"\nimport {listDomain} from \"@/api/dict/domain\";\nimport dayjs from \"dayjs\";\n\nexport default {\n  name: \"OperationSystemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      deptOptions: [],\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      managerData: [],\n      networkDomainOptions: [],\n      vendorsData: [],\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      uploadType: ['waitingInsuranceFilingScan', 'evaluationReport', 'netTopo', 'operateHandbook'],\n      selectType: ['systemType', 'construct', 'loginType', 'technical', 'deploy', 'state', 'protectGrade', 'evaluationResults', 'evaluationStatus', 'coverArea'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.getDeptTree();\n    this.getManagerList();\n    this.getNetworkDomainTree();\n    this.getVendorsData();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  computed: {\n    // 获取服务器名称映射\n    getServerName() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.assetName || ''\n    },\n    // 获取服务器IP映射\n    getServerIp() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.ip || ''\n    },\n    processedManagerList() {\n      // 去重\n      const ids = [...new Set(\n        (this.form.manager || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.managerData.find(u =>\n          Number(u.userId) === Number(id)\n        );\n        return {\n          id,\n          name: user?.nickName || '未知用户',\n          phone: user?.phonenumber || ''\n        };\n      });\n    },\n    processedVendorsList() {\n      const ids = [...new Set(\n        (this.form.vendors || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.vendorsData.find(u =>\n          Number(u.id) === Number(id)\n        );\n        return {\n          id,\n          name: user?.vendorName || '未知用户',\n        };\n      });\n    },\n    processedServiceGroups() {\n      if (!this.businessForm.serviceGroup) return []\n      return this.businessForm.serviceGroup.split(',')\n        .map(val => this.dict.type['serve_group'].find(d => d.value === val)?.label || val)\n    }\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if (Object.keys(item).length > 0) {\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    },\n    'form.systemType': {\n      handler(newVal, oldVal) {\n        if (newVal) {\n          this.form.systemType = newVal.toString();\n        }\n      }\n    },\n  },\n  methods: {\n    getFieldValue(field) {\n      // 其他基本信息字段格式化\n      let filterArr = ['isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'iskey', 'isOpenNetwork']\n      if (filterArr.includes(field.fieldKey)) {\n        return this.form[field.fieldKey] === 'Y' ? '是' : '否';\n      }\n      if(field.fieldKey === 'hwIsTrueShutDown'){\n        return this.dict.type.hw_is_true_shut_down.find(d => d.value === this.form[field.fieldKey])?.label || this.form[field.fieldKey];\n      }\n      if(field.fieldKey === 'uodTime'){\n        return dayjs(this.form[field.fieldKey]).format('YYYY-MM-DD HH:mm:ss')\n      }\n      return this.form[field.fieldKey];\n    },\n\n    getFieldSpan(field) {\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];\n      if (fullSpanFields.includes(field.fieldKey)) return 3;\n      // 其他字段默认占8列\n      return 1;\n    },\n    // 判断字段是否显示\n    shouldShowField(field) {\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType === '12';\n      }\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down',\n        coverArea: 'cover_area'\n      };\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    getAllServerList() {\n      listAllOverview({\"assetClass\": 4}).then(res => {\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(() => {\n          this.afterInit = true;\n        })\n      } else {\n        this.afterInit = true;\n      }\n    },\n\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n\n    serverSelect(data) {\n      if (data) {\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      if (this.$editable.value) {\n        getAllDeptTree().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      } else {\n        deptTreeSelect().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      }\n    },\n\n    //  查询所有责任人/电话\n    getManagerList() {\n      listUser({\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        isAllData: true,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      }).then(response => {\n        this.managerData = response.rows;\n      });\n    },\n\n    /** 获取主部署网络 */\n    getNetworkDomainTree() {\n      listDomain().then(response => {\n        this.networkDomainOptions = response.data\n      });\n    },\n\n    /* 获取开发合作企业 */\n    getVendorsData() {\n      listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        isAsc: 'desc',\n        orderByColumn: null,\n        pageNum: 1,\n        pageSize: 10,\n        vendorCode: null,\n        vendorName: null,\n        vendorManageName: null,\n      }).then(response => {\n        this.vendorsData = response.rows;\n      });\n    }\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.server-display {\n  line-height: 1.8;\n  display: flex;\n}\n\n.server-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 5px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAsRA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,cAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,WAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,aAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,WAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,SAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,OAAA,GAAAZ,sBAAA,CAAAF,OAAA;AACA,IAAAe,UAAA,GAAAb,sBAAA,CAAAF,OAAA;AACA,IAAAgB,OAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,eAAA,GAAAf,sBAAA,CAAAF,OAAA;AACA,IAAAkB,SAAA,GAAAlB,OAAA;AACA,IAAAmB,WAAA,GAAAjB,sBAAA,CAAAF,OAAA;AACA,IAAAoB,KAAA,GAAApB,OAAA;AACA,IAAAqB,OAAA,GAAArB,OAAA;AACA,IAAAsB,MAAA,GAAApB,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAuB,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA,GACA,eACA,cACA,cACA,iBACA,kBACA,eACA,oBACA,eACA,kBACA,iBACA,cACA,eACA,sBACA,qBACA,mBACA,uBACA;EACAC,MAAA;IACAC,SAAA;MACAC,OAAA;QAAAC,KAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;MACAP,OAAA;IACA;IACAQ,QAAA,EAAAC,QAAA;IACAC,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAY,QAAA;MACAR,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAa,SAAA;MACAT,IAAA,EAAAU,KAAA;MACAd,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAe,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MACAC,iBAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;QACAC,OAAA;MACA;MACAC,WAAA;MACAC,EAAA,EAAAC,uBAAA;MACAC,cAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,IAAA;QACA;QACA;QACA;QACA;MACA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;MACAC,wBAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,WAAA;IACA,KAAAC,cAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,cAAA;IACA,KAAAC,SAAA;MACA,IAAAN,KAAA,CAAAjB,cAAA;QACAiB,KAAA,CAAAd,YAAA;QACAc,KAAA,CAAAb,iBAAA;MACA;MACAa,KAAA,CAAAO,KAAA;MACAP,KAAA,CAAAQ,IAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,iBAAAC,EAAA;QAAA,IAAAC,qBAAA;QAAA,SAAAA,qBAAA,GAAAF,MAAA,CAAAjB,aAAA,CAAAoB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAxD,OAAA,KAAAqD,EAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAG,SAAA;MAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,iBAAAN,EAAA;QAAA,IAAAO,qBAAA;QAAA,SAAAA,qBAAA,GAAAD,MAAA,CAAAxB,aAAA,CAAAoB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAxD,OAAA,KAAAqD,EAAA;QAAA,gBAAAO,qBAAA,uBAAAA,qBAAA,CAAAC,EAAA;MAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,GAAA,OAAAC,mBAAA,CAAApE,OAAA,MAAAqE,GAAA,CACA,MAAAhD,IAAA,CAAAiD,OAAA,QACAC,KAAA,MACAC,MAAA,CAAA7D,OAAA,CACA;MAEA,OAAAwD,GAAA,CAAAM,GAAA,WAAAjB,EAAA;QACA,IAAAkB,IAAA,GAAAR,MAAA,CAAAlC,WAAA,CAAA0B,IAAA,WAAAiB,CAAA;UAAA,OACArE,MAAA,CAAAqE,CAAA,CAAAC,MAAA,MAAAtE,MAAA,CAAAkD,EAAA;QAAA,CACA;QACA;UACAA,EAAA,EAAAA,EAAA;UACA3E,IAAA,GAAA6F,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAG,QAAA;UACAC,KAAA,GAAAJ,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAK,WAAA;QACA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAAd,GAAA,OAAAC,mBAAA,CAAApE,OAAA,MAAAqE,GAAA,CACA,MAAAhD,IAAA,CAAA6D,OAAA,QACAX,KAAA,MACAC,MAAA,CAAA7D,OAAA,CACA;MAEA,OAAAwD,GAAA,CAAAM,GAAA,WAAAjB,EAAA;QACA,IAAAkB,IAAA,GAAAO,MAAA,CAAA/C,WAAA,CAAAwB,IAAA,WAAAiB,CAAA;UAAA,OACArE,MAAA,CAAAqE,CAAA,CAAAnB,EAAA,MAAAlD,MAAA,CAAAkD,EAAA;QAAA,CACA;QACA;UACAA,EAAA,EAAAA,EAAA;UACA3E,IAAA,GAAA6F,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAS,UAAA;QACA;MACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,UAAA/D,YAAA,CAAAgE,YAAA;MACA,YAAAhE,YAAA,CAAAgE,YAAA,CAAAf,KAAA,MACAE,GAAA,WAAAc,GAAA;QAAA,IAAAC,qBAAA;QAAA,SAAAA,qBAAA,GAAAH,MAAA,CAAAI,IAAA,CAAArF,IAAA,gBAAAsD,IAAA,WAAAgC,CAAA;UAAA,OAAAA,CAAA,CAAAzF,KAAA,KAAAsF,GAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAG,KAAA,KAAAJ,GAAA;MAAA;IACA;EACA;EACAK,SAAA,WAAAA,UAAA;IAAA,IAAAC,MAAA;IACA,KAAA3C,SAAA;MACA2C,MAAA,CAAA1C,KAAA;MACA0C,MAAA,CAAAzC,IAAA;IACA;EACA;EACA0C,KAAA;IACA1E,iBAAA;MACA2E,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,MAAA;UACAF,MAAA,CAAAG,OAAA,WAAAC,IAAA,EAAAC,KAAA;YACA,IAAAC,MAAA,CAAAC,IAAA,CAAAH,IAAA,EAAAF,MAAA;cACAE,IAAA,CAAAI,MAAA,OAAAC,yBAAA;YACA;UACA;QACA;MACA;IACA;IACA;MACAV,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA;UACA,KAAA3E,IAAA,CAAAqF,UAAA,GAAAV,MAAA,CAAAW,QAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,SAAA;MACA,IAAAA,SAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAyF,KAAA,CAAAI,QAAA;MACA;MACA,IAAAJ,KAAA,CAAAI,QAAA;QAAA,IAAAC,qBAAA;QACA,SAAAA,qBAAA,QAAA1B,IAAA,CAAArF,IAAA,CAAAgH,oBAAA,CAAA1D,IAAA,WAAAgC,CAAA;UAAA,OAAAA,CAAA,CAAAzF,KAAA,KAAA8G,MAAA,CAAA1F,IAAA,CAAAyF,KAAA,CAAAI,QAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAxB,KAAA,UAAAtE,IAAA,CAAAyF,KAAA,CAAAI,QAAA;MACA;MACA,IAAAJ,KAAA,CAAAI,QAAA;QACA,WAAAG,cAAA,OAAAhG,IAAA,CAAAyF,KAAA,CAAAI,QAAA,GAAAI,MAAA;MACA;MACA,YAAAjG,IAAA,CAAAyF,KAAA,CAAAI,QAAA;IACA;IAEAK,YAAA,WAAAA,aAAAT,KAAA;MACA,IAAAU,cAAA;MACA,IAAAA,cAAA,CAAAP,QAAA,CAAAH,KAAA,CAAAI,QAAA;MACA;MACA;IACA;IACA;IACAO,eAAA,WAAAA,gBAAAX,KAAA;MACA,IAAAA,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAqF,UAAA;MACA;MACA,IAAAI,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAqG,OAAA;MACA;MACA,IAAAZ,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAsG,QAAA;MACA;MACA,IAAAb,KAAA,CAAAI,QAAA;QACA,YAAAvF,cAAA;MACA;MACA;IACA;IAEAiG,cAAA,WAAAA,eAAAV,QAAA;MACA,IAAAW,OAAA;QACAnB,UAAA;QACAoB,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,KAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,SAAA;MACA;MACA,YAAA9C,IAAA,CAAArF,IAAA,CAAAyH,OAAA,CAAAX,QAAA;IACA;IAEArE,gBAAA,WAAAA,iBAAA;MAAA,IAAA2F,MAAA;MACA,IAAAC,yBAAA;QAAA;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAlG,aAAA,GAAAqG,GAAA,CAAA5H,IAAA;MACA;IACA;IACA,UACAqC,IAAA,WAAAA,KAAA;MAAA,IAAAwF,OAAA;MAAA,WAAAC,kBAAA,CAAA7I,OAAA,mBAAA8I,oBAAA,CAAA9I,OAAA,IAAA+I,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAA9I,OAAA,IAAAiJ,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,KAEAT,OAAA,CAAAzI,OAAA;gBAAAgJ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,2BAAA,EAAAV,OAAA,CAAAzI,OAAA,EAAAuI,IAAA,WAAAa,QAAA;gBACA;gBACAX,OAAA,CAAAvH,IAAA,CAAAlB,OAAA,GAAAyI,OAAA,CAAAzI,OAAA;gBACAyI,OAAA,CAAAvH,IAAA,GAAAkI,QAAA,CAAAxI,IAAA,CAAAyI,aAAA;gBACA,IAAAC,mBAAA;kBAAA,WAAA/H,uBAAA,UAAAkH,OAAA,CAAAc,KAAA;gBAAA,GAAAhB,IAAA,WAAAiB,IAAA;kBACA,KAAAA,IAAA;oBACA;kBACA;kBACA,IAAAA,IAAA,YAAA7I,KAAA;oBACA6I,IAAA,CAAAxD,OAAA,WAAAC,IAAA;sBAAA,OAAAA,IAAA,CAAAwD,OAAA;oBAAA;kBACA;oBACAD,IAAA,CAAAC,OAAA;kBACA;gBACA;gBACA;gBACAhB,OAAA,CAAAtH,YAAA,CAAAnB,OAAA,GAAAyI,OAAA,CAAAzI,OAAA;gBACAyI,OAAA,CAAAtH,YAAA,GAAAiI,QAAA,CAAAxI,IAAA,CAAA8I,sBAAA;gBACAjB,OAAA,CAAAtH,YAAA,CAAAwI,QAAA,GAAAlB,OAAA,CAAAtH,YAAA,CAAAwI,QAAA,YAAAlB,OAAA,CAAAtH,YAAA,CAAAwI,QAAA;gBACAlB,OAAA,CAAAtH,YAAA,CAAAyI,iBAAA,GAAAnB,OAAA,CAAAtH,YAAA,CAAAyI,iBAAA,YAAAnB,OAAA,CAAAtH,YAAA,CAAAyI,iBAAA;gBACAnB,OAAA,CAAAtH,YAAA,CAAA0I,kBAAA,GAAApB,OAAA,CAAAtH,YAAA,CAAA0I,kBAAA,YAAApB,OAAA,CAAAtH,YAAA,CAAA0I,kBAAA;gBACApB,OAAA,CAAAxH,iBAAA,GAAAmI,QAAA,CAAAxI,IAAA,CAAA8I,sBAAA,CAAAI,aAAA;gBACA,IAAArB,OAAA,CAAAxH,iBAAA,CAAA8E,MAAA;kBACA,IAAAgE,CAAA;kBACA,OAAAA,CAAA,OAAAtB,OAAA,CAAAxH,iBAAA,CAAA8E,MAAA;oBACA0C,OAAA,CAAAxH,iBAAA,CAAA+I,IAAA;kBACA;gBACA;cACA,GAAAC,OAAA;gBACAxB,OAAA,CAAApG,SAAA;cACA;YAAA;cAAA2G,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAT,OAAA,CAAApG,SAAA;YAAA;YAAA;cAAA,OAAA2G,QAAA,CAAAkB,IAAA;UAAA;QAAA,GAAArB,OAAA;MAAA;IAEA;IAGA,WACA7F,KAAA,WAAAA,MAAA;MACA,KAAA9B,IAAA;QACAlB,OAAA,EAAAmK,SAAA;QACAC,SAAA,EAAAD,SAAA;QACA1G,SAAA,EAAA0G,SAAA;QACAE,eAAA,EAAAF,SAAA;QACAG,gBAAA,EAAAH,SAAA;QACAhG,OAAA,EAAAgG,SAAA;QACAI,SAAA,EAAAJ,SAAA;QACA5D,UAAA,EAAA4D,SAAA;QACAxF,KAAA,EAAAwF,SAAA;QACAK,SAAA,EAAAL,SAAA;QACAM,aAAA,EAAAN,SAAA;QACAO,UAAA,EAAAP,SAAA;QACAQ,cAAA,EAAAR,SAAA;QACAxC,SAAA,EAAAwC,SAAA;QACAS,OAAA,EAAAT,SAAA;QACAU,OAAA,EAAAV,SAAA;QACAhF,YAAA,EAAAgF,SAAA;QACAW,SAAA,EAAAX,SAAA;QACAY,UAAA,EAAAZ,SAAA;QACAa,SAAA,EAAAb,SAAA;QACAc,UAAA,EAAAd,SAAA;QACAe,GAAA,EAAAf,SAAA;QACAgB,GAAA,EAAAhB,SAAA;QACAtC,SAAA,EAAAsC,SAAA;QACArC,MAAA,EAAAqC,SAAA;QACAiB,OAAA,EAAAjB,SAAA;QACAkB,MAAA,EAAAlB,SAAA;QACAmB,KAAA,EAAAnB,SAAA;QACAoB,OAAA,EAAApB,SAAA;QACAqB,MAAA;QACAC,MAAA,EAAAtB,SAAA;QACAuB,MAAA,EAAAvB,SAAA;QACAwB,KAAA,EAAAxB,SAAA;QACAyB,MAAA,EAAAzB,SAAA;QACA5C,OAAA,EAAA4C,SAAA;QACA3C,QAAA,EAAA2C,SAAA;QACA0B,SAAA,EAAA1B,SAAA;QACA2B,UAAA,EAAA3B,SAAA;QACA4B,QAAA,EAAA5B,SAAA;QACA6B,MAAA,EAAA7B,SAAA;QACA1F,MAAA,EAAA0F,SAAA;QACA8B,MAAA,EAAA9B,SAAA;QACA+B,MAAA,EAAA/B,SAAA;QACApF,OAAA,EAAAoF,SAAA;QACAgC,MAAA,EAAAhC,SAAA;QACAiC,IAAA,EAAAjC,SAAA;QACAkC,SAAA,EAAAlC,SAAA;QACAmC,QAAA,EAAAnC,SAAA;QACAoC,QAAA,EAAApC,SAAA;QACAqC,OAAA,EAAArC,SAAA;QACAsC,OAAA,EAAAtC,SAAA;QACAuC,IAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACA,KAAAzL,YAAA;QACA0L,gBAAA,EAAA1C,SAAA;QACAR,QAAA,EAAAQ,SAAA;QACAP,iBAAA,EAAAO,SAAA;QACAN,kBAAA,EAAAM;MACA;MACA,KAAA2C,SAAA;MACA,KAAAA,SAAA;IACA;IAEAC,YAAA,WAAAA,aAAAnM,IAAA;MACA,IAAAA,IAAA;QACA,KAAAoM,IAAA,MAAA9L,IAAA,uBAAAN,IAAA,CAAA0D,GAAA,WAAA2B,IAAA;UAAA,OAAAA,IAAA,CAAAgH,QAAA;QAAA;MACA;IACA;IAEA,aACAtK,WAAA,WAAAA,YAAA;MAAA,IAAAuK,OAAA;MACA,SAAAtN,SAAA,CAAAE,KAAA;QACA,IAAAqN,oBAAA,IAAA5E,IAAA,WAAAa,QAAA;UACA8D,OAAA,CAAA7L,WAAA,OAAA+L,yBAAA,EAAAhE,QAAA,CAAAxI,IAAA;QACA;MACA;QACA,IAAAyM,oBAAA,IAAA9E,IAAA,WAAAa,QAAA;UACA8D,OAAA,CAAA7L,WAAA,OAAA+L,yBAAA,EAAAhE,QAAA,CAAAxI,IAAA;QACA;MACA;IACA;IAEA;IACAgC,cAAA,WAAAA,eAAA;MAAA,IAAA0K,OAAA;MACA,IAAAC,cAAA;QACAC,KAAA;QACAC,aAAA;QACAC,SAAA;QACAC,QAAA;QACAjJ,QAAA;QACAE,WAAA;MACA,GAAA2D,IAAA,WAAAa,QAAA;QACAkE,OAAA,CAAAzL,WAAA,GAAAuH,QAAA,CAAAwE,IAAA;MACA;IACA;IAEA,cACA/K,oBAAA,WAAAA,qBAAA;MAAA,IAAAgL,OAAA;MACA,IAAAC,kBAAA,IAAAvF,IAAA,WAAAa,QAAA;QACAyE,OAAA,CAAA/L,oBAAA,GAAAsH,QAAA,CAAAxI,IAAA;MACA;IACA;IAEA,cACAkC,cAAA,WAAAA,eAAA;MAAA,IAAAiL,OAAA;MACA,IAAAC,+BAAA;QACAC,aAAA,OAAAjO,OAAA;QACAkO,eAAA,OAAAhN,IAAA,CAAA6D,OAAA;QACAyI,KAAA;QACAC,aAAA;QACAU,OAAA;QACAC,QAAA;QACAC,UAAA;QACArJ,UAAA;QACAsJ,gBAAA;MACA,GAAA/F,IAAA,WAAAa,QAAA;QACA2E,OAAA,CAAAhM,WAAA,GAAAqH,QAAA,CAAAwE,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}