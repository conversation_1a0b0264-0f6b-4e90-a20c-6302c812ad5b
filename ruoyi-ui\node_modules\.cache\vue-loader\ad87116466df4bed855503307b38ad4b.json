{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\monitor\\deviceConfig\\index.vue?vue&type=template&id=77be25aa", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\monitor\\deviceConfig\\index.vue", "mtime": 1755138396950}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}