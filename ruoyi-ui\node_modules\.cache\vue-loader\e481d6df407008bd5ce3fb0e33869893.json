{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue", "mtime": 1755138396954}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Z2V0QXBwbGljYXRpb259IGZyb20gIkAvYXBpL3NhZmUvYXBwbGljYXRpb24iOwppbXBvcnQgQXBwbGljYXRpb25MaW5rIGZyb20gJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25MaW5rJzsKaW1wb3J0IEFwcGxpY2F0aW9uU2l0ZSBmcm9tICdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uU2l0ZSc7CmltcG9ydCBVc2VyU2VsZWN0IGZyb20gJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvdXNlclNlbGVjdCc7CmltcG9ydCBEZXB0U2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvZGVwdFNlbGVjdCc7CmltcG9ydCBOZXR3b3JrU2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvbmV0d29ya1NlbGVjdCc7CmltcG9ydCBEeW5hbWljVGFnIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljVGFnJzsKaW1wb3J0IFZlbmRvclNlbGVjdDIgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC92ZW5kb3JTZWxlY3QyJzsKaW1wb3J0IERpY3RTZWxlY3QgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC9kaWN0U2VsZWN0JzsKaW1wb3J0IHtmbGF0dGVuVHJlZURhdGEsIGZsYXR0ZW5UcmVlVG9BcnJheSwgZ2V0VmFsRnJvbU9iamVjdH0gZnJvbSAiQC91dGlscyI7CmltcG9ydCB7Z2VuZXJhdGVTZWN1cmVVVUlELCB3YWl0Rm9yVmFsdWV9IGZyb20gIkAvdXRpbHMvcnVveWkiOwppbXBvcnQge2xpc3RWZW5kb3JCeUFwcGxpY2F0aW9ufSBmcm9tICJAL2FwaS9zYWZlL3ZlbmRvciI7CmltcG9ydCBzZXJ2ZXJFViBmcm9tICJAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uSGFyZHdhcmUvc2VydmVyRVYudnVlIjsKaW1wb3J0IGRhdGVFViBmcm9tICJAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uSGFyZHdhcmUvZGF0ZUVWLnZ1ZSI7CmltcG9ydCBuZXR3b3JrRVYgZnJvbSAiQC92aWV3cy9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hcHBsaWNhdGlvbkhhcmR3YXJlL25ldHdvcmtFVi52dWUiOwppbXBvcnQgc2FmZUVWIGZyb20gIkAvdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25IYXJkd2FyZS9zYWZlRVYudnVlIjsKaW1wb3J0IG92ZXJWaWV3U2VsZWN0IGZyb20gIkAvdmlld3MvY29tcG9uZW50cy9zZWxlY3Qvb3ZlclZpZXdTZWxlY3QudnVlIjsKaW1wb3J0IHtsaXN0QWxsT3ZlcnZpZXd9IGZyb20gIkAvYXBpL3NhZmUvb3ZlcnZpZXciOwppbXBvcnQgRWRpdFNlcnZlciBmcm9tICJAL3ZpZXdzL3NhZmUvc2VydmVyL2VkaXRTZXJ2ZXIudnVlIjsKaW1wb3J0IHtnZXRBbGxEZXB0VHJlZSwgZGVwdFRyZWVTZWxlY3QsIGxpc3RVc2VyfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciIKaW1wb3J0IHtsaXN0RG9tYWlufSBmcm9tICJAL2FwaS9kaWN0L2RvbWFpbiI7CmltcG9ydCBkYXlqcyBmcm9tICJkYXlqcyI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk9wZXJhdGlvblN5c3RlbURldGFpbHMiLAogIGNvbXBvbmVudHM6IHsKICAgIEVkaXRTZXJ2ZXIsCiAgICBvdmVyVmlld1NlbGVjdCwKICAgIHNhZmVFViwKICAgIG5ldHdvcmtFViwKICAgIGRhdGVFViwKICAgIHNlcnZlckVWLAogICAgQXBwbGljYXRpb25MaW5rLAogICAgQXBwbGljYXRpb25TaXRlLAogICAgVXNlclNlbGVjdCwKICAgIERlcHRTZWxlY3QsCiAgICBOZXR3b3JrU2VsZWN0LAogICAgRGljdFNlbGVjdCwKICAgIER5bmFtaWNUYWcsCiAgICBWZW5kb3JTZWxlY3QyLAogIH0sCiAgZGljdHM6IFsKICAgICdzZXJ2ZV9ncm91cCcsCiAgICAnY292ZXJfYXJlYScsCiAgICAnc3lzX3llc19ubycsCiAgICAnYXBwX25ldF9zY2FsZScsCiAgICAnY29uc3RydWN0X3R5cGUnLAogICAgJ3N5c3RlbV90eXBlJywKICAgICdwcm90ZWN0aW9uX2dyYWRlJywKICAgICdhc3NldF9zdGF0ZScsCiAgICAnYXBwX2xvZ2luX3R5cGUnLAogICAgJ2FwcF90ZWNobmljYWwnLAogICAgJ2FwcF9kZXBsb3knLAogICAgJ2FwcF9zdG9yYWdlJywKICAgICdldmFsdWF0aW9uX3Jlc3VsdHMnLAogICAgJ2V2YWx1YXRpb25fc3RhdHVzJywKICAgICdpc19vcGVuX25ldHdvcmsnLAogICAgJ2h3X2lzX3RydWVfc2h1dF9kb3duJwogIF0sCiAgaW5qZWN0OiB7CiAgICAkZWRpdGFibGU6IHsKICAgICAgZGVmYXVsdDoge3ZhbHVlOiB0cnVlfSwKICAgIH0KICB9LAogIHByb3BzOiB7CiAgICBhc3NldElkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiBmYWxzZSwKICAgICAgZGVmYXVsdDogbnVsbCwKICAgIH0sCiAgICBjaGFuZ2VJZDogRnVuY3Rpb24sCiAgICByZWFkb25seTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZSwKICAgIH0sCiAgICBkaXNhYmxlZDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZSwKICAgIH0sCiAgICBhc3NldExpc3Q6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9LAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBjb2xsYXBzZU5hbWVzOiBbJzEnLCAnMicsICczJywgJzQnLCAnNSddLAogICAgICB2ZW5kb3JzZGF0YTogJzEnLAogICAgICB1c2VyZGF0YTogJzEnLAogICAgICBmdW5jdGlvblN0YXRlTGlzdDogW3t9LCB7fSwge31dLAogICAgICAvLyDln7rmnKzkv6Hmga/ooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOS4muWKoeS/oeaBr+ihqOWNleWPguaVsAogICAgICBidXNpbmVzc0Zvcm06IHsKICAgICAgICBkZWxMaXN0OiBbXQogICAgICB9LAogICAgICBkZXB0T3B0aW9uczogW10sCiAgICAgIGd2OiBnZXRWYWxGcm9tT2JqZWN0LAogICAgICBkZXBsb3lMb2NhdGlvbjogbG9jYWxTdG9yYWdlLmdldEl0ZW0oImRsIiksCiAgICAgIG1hbmFnZXJMYWJlbDogJ+i0o+S7u+S6ui/nlLXor50nLAogICAgICBtYW5hZ2VQbGFjZWhvbGRlcjogJ+ivt+mAieaLqei0o+S7u+S6uicsCiAgICAgIG1hbmFnZXJEYXRhOiBbXSwKICAgICAgbmV0d29ya0RvbWFpbk9wdGlvbnM6IFtdLAogICAgICB2ZW5kb3JzRGF0YTogW10sCiAgICAgIHJlZnM6IHsKICAgICAgICAnbmV0d29ya0VWJzogIuaJgOWuieijheacjeWKoeWZqOeOr+WigyIsCiAgICAgICAgJ3NhZmVFVic6ICfmiYDlronoo4XmlbDmja7njq/looMnLAogICAgICAgICdzZXJ2ZXJFVic6ICflhbPogZTnvZHnu5zorr7lpIcnLAogICAgICAgICdkYXRlRVYnOiAi5YWz6IGU5a6J5YWo6K6+5aSHIgogICAgICB9LAogICAgICBjb2xsYXBzZTogWycxJywgJzInLCAnMycsICc0J10sCiAgICAgIHNob3dBZGRTZXJ2ZXI6IGZhbHNlLAogICAgICBzZXJ2ZXJPcHRpb25zOiBbXSwKICAgICAgY3VycmVudEFzc29jaWF0aW9uU2VydmVyOiBbXSwKICAgICAgYWZ0ZXJJbml0OiBmYWxzZSwKICAgICAgdXBsb2FkVHlwZTogWyd3YWl0aW5nSW5zdXJhbmNlRmlsaW5nU2NhbicsICdldmFsdWF0aW9uUmVwb3J0JywgJ25ldFRvcG8nLCAnb3BlcmF0ZUhhbmRib29rJ10sCiAgICAgIHNlbGVjdFR5cGU6IFsnc3lzdGVtVHlwZScsICdjb25zdHJ1Y3QnLCAnbG9naW5UeXBlJywgJ3RlY2huaWNhbCcsICdkZXBsb3knLCAnc3RhdGUnLCAncHJvdGVjdEdyYWRlJywgJ2V2YWx1YXRpb25SZXN1bHRzJywgJ2V2YWx1YXRpb25TdGF0dXMnLCAnY292ZXJBcmVhJ10sCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRBbGxTZXJ2ZXJMaXN0KCk7CiAgICB0aGlzLmdldERlcHRUcmVlKCk7CiAgICB0aGlzLmdldE1hbmFnZXJMaXN0KCk7CiAgICB0aGlzLmdldE5ldHdvcmtEb21haW5UcmVlKCk7CiAgICB0aGlzLmdldFZlbmRvcnNEYXRhKCk7CiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgIGlmICh0aGlzLmRlcGxveUxvY2F0aW9uID09PSAnZmFpcicpIHsKICAgICAgICB0aGlzLm1hbmFnZXJMYWJlbCA9ICfotKPku7vmsJHoraYv55S16K+dJwogICAgICAgIHRoaXMubWFuYWdlUGxhY2Vob2xkZXIgPSAn6K+36YCJ5oup6LSj5Lu75rCR6K2mJwogICAgICB9CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5pbml0KCkKICAgIH0pOwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOiOt+WPluacjeWKoeWZqOWQjeensOaYoOWwhAogICAgZ2V0U2VydmVyTmFtZSgpIHsKICAgICAgcmV0dXJuIChpZCkgPT4gdGhpcy5zZXJ2ZXJPcHRpb25zLmZpbmQocyA9PiBzLmFzc2V0SWQgPT09IGlkKT8uYXNzZXROYW1lIHx8ICcnCiAgICB9LAogICAgLy8g6I635Y+W5pyN5Yqh5ZmoSVDmmKDlsIQKICAgIGdldFNlcnZlcklwKCkgewogICAgICByZXR1cm4gKGlkKSA9PiB0aGlzLnNlcnZlck9wdGlvbnMuZmluZChzID0+IHMuYXNzZXRJZCA9PT0gaWQpPy5pcCB8fCAnJwogICAgfSwKICAgIHByb2Nlc3NlZE1hbmFnZXJMaXN0KCkgewogICAgICAvLyDljrvph40KICAgICAgY29uc3QgaWRzID0gWy4uLm5ldyBTZXQoCiAgICAgICAgKHRoaXMuZm9ybS5tYW5hZ2VyIHx8ICcnKQogICAgICAgICAgLnNwbGl0KCcsJykKICAgICAgICAgIC5maWx0ZXIoQm9vbGVhbikKICAgICAgKV07CgogICAgICByZXR1cm4gaWRzLm1hcChpZCA9PiB7CiAgICAgICAgY29uc3QgdXNlciA9IHRoaXMubWFuYWdlckRhdGEuZmluZCh1ID0+CiAgICAgICAgICBOdW1iZXIodS51c2VySWQpID09PSBOdW1iZXIoaWQpCiAgICAgICAgKTsKICAgICAgICByZXR1cm4gewogICAgICAgICAgaWQsCiAgICAgICAgICBuYW1lOiB1c2VyPy5uaWNrTmFtZSB8fCAn5pyq55+l55So5oi3JywKICAgICAgICAgIHBob25lOiB1c2VyPy5waG9uZW51bWJlciB8fCAnJwogICAgICAgIH07CiAgICAgIH0pOwogICAgfSwKICAgIHByb2Nlc3NlZFZlbmRvcnNMaXN0KCkgewogICAgICBjb25zdCBpZHMgPSBbLi4ubmV3IFNldCgKICAgICAgICAodGhpcy5mb3JtLnZlbmRvcnMgfHwgJycpCiAgICAgICAgICAuc3BsaXQoJywnKQogICAgICAgICAgLmZpbHRlcihCb29sZWFuKQogICAgICApXTsKCiAgICAgIHJldHVybiBpZHMubWFwKGlkID0+IHsKICAgICAgICBjb25zdCB1c2VyID0gdGhpcy52ZW5kb3JzRGF0YS5maW5kKHUgPT4KICAgICAgICAgIE51bWJlcih1LmlkKSA9PT0gTnVtYmVyKGlkKQogICAgICAgICk7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGlkLAogICAgICAgICAgbmFtZTogdXNlcj8udmVuZG9yTmFtZSB8fCAn5pyq55+l55So5oi3JywKICAgICAgICB9OwogICAgICB9KTsKICAgIH0sCiAgICBwcm9jZXNzZWRTZXJ2aWNlR3JvdXBzKCkgewogICAgICBpZiAoIXRoaXMuYnVzaW5lc3NGb3JtLnNlcnZpY2VHcm91cCkgcmV0dXJuIFtdCiAgICAgIHJldHVybiB0aGlzLmJ1c2luZXNzRm9ybS5zZXJ2aWNlR3JvdXAuc3BsaXQoJywnKQogICAgICAgIC5tYXAodmFsID0+IHRoaXMuZGljdC50eXBlWydzZXJ2ZV9ncm91cCddLmZpbmQoZCA9PiBkLnZhbHVlID09PSB2YWwpPy5sYWJlbCB8fCB2YWwpCiAgICB9CiAgfSwKICBhY3RpdmF0ZWQoKSB7CiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5pbml0KCkKICAgIH0pOwogIH0sCiAgd2F0Y2g6IHsKICAgIGZ1bmN0aW9uU3RhdGVMaXN0OiB7CiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsKICAgICAgICBpZiAobmV3VmFsICYmIG5ld1ZhbC5sZW5ndGggPiAwKSB7CiAgICAgICAgICBuZXdWYWwuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICAgICAgaWYgKE9iamVjdC5rZXlzKGl0ZW0pLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBpdGVtLnRlbXBJZCA9IGdlbmVyYXRlU2VjdXJlVVVJRCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSwKICAgIH0sCiAgICAnZm9ybS5zeXN0ZW1UeXBlJzogewogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCkgewogICAgICAgICAgdGhpcy5mb3JtLnN5c3RlbVR5cGUgPSBuZXdWYWwudG9TdHJpbmcoKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRGaWVsZFZhbHVlKGZpZWxkKSB7CiAgICAgIC8vIOWFtuS7luWfuuacrOS/oeaBr+Wtl+auteagvOW8j+WMlgogICAgICBsZXQgZmlsdGVyQXJyID0gWydpc2Jhc2UnLCAnaXNsb2cnLCAnaXNhZGFwdCcsICdpc2NpcGhlcicsICdpc3BsYW4nLCAnaXNsaW5rJywgJ2lza2V5JywgJ2lzT3Blbk5ldHdvcmsnXQogICAgICBpZiAoZmlsdGVyQXJyLmluY2x1ZGVzKGZpZWxkLmZpZWxkS2V5KSkgewogICAgICAgIHJldHVybiB0aGlzLmZvcm1bZmllbGQuZmllbGRLZXldID09PSAnWScgPyAn5pivJyA6ICflkKYnOwogICAgICB9CiAgICAgIGlmKGZpZWxkLmZpZWxkS2V5ID09PSAnaHdJc1RydWVTaHV0RG93bicpewogICAgICAgIHJldHVybiB0aGlzLmRpY3QudHlwZS5od19pc190cnVlX3NodXRfZG93bi5maW5kKGQgPT4gZC52YWx1ZSA9PT0gdGhpcy5mb3JtW2ZpZWxkLmZpZWxkS2V5XSk/LmxhYmVsIHx8IHRoaXMuZm9ybVtmaWVsZC5maWVsZEtleV07CiAgICAgIH0KICAgICAgaWYoZmllbGQuZmllbGRLZXkgPT09ICd1b2RUaW1lJyl7CiAgICAgICAgcmV0dXJuIGRheWpzKHRoaXMuZm9ybVtmaWVsZC5maWVsZEtleV0pLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbTpzcycpCiAgICAgIH0KICAgICAgcmV0dXJuIHRoaXMuZm9ybVtmaWVsZC5maWVsZEtleV07CiAgICB9LAoKICAgIGdldEZpZWxkU3BhbihmaWVsZCkgewogICAgICBjb25zdCBmdWxsU3BhbkZpZWxkcyA9IFsnYXNzb2NpYXRpb25TZXJ2ZXInLCAnbmV0VG9wbycsICduZXRNZW1vJywgJ2V2YWx1YXRpb25SZXBvcnQnLCAnd2FpdGluZ0luc3VyYW5jZUZpbGluZ1NjYW4nXTsKICAgICAgaWYgKGZ1bGxTcGFuRmllbGRzLmluY2x1ZGVzKGZpZWxkLmZpZWxkS2V5KSkgcmV0dXJuIDM7CiAgICAgIC8vIOWFtuS7luWtl+autem7mOiupOWNoDjliJcKICAgICAgcmV0dXJuIDE7CiAgICB9LAogICAgLy8g5Yik5pat5a2X5q615piv5ZCm5pi+56S6CiAgICBzaG91bGRTaG93RmllbGQoZmllbGQpIHsKICAgICAgaWYgKGZpZWxkLmZpZWxkS2V5ID09PSAnb3RoZXJTeXN0ZW1Ob3RlcycpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLnN5c3RlbVR5cGUgPT09ICcxMic7CiAgICAgIH0KICAgICAgaWYgKGZpZWxkLmZpZWxkS2V5ID09PSAnYWRhcHREYXRlJykgewogICAgICAgIHJldHVybiB0aGlzLmZvcm0uaXNhZGFwdCA9PT0gJ1knOwogICAgICB9CiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ2NpcGhlckRhdGUnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5pc2NpcGhlciA9PT0gJ1knOwogICAgICB9CiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ2lzbGluaycpIHsKICAgICAgICByZXR1cm4gdGhpcy5kZXBsb3lMb2NhdGlvbiA9PT0gJ2ZhaXInOwogICAgICB9CiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKCiAgICBnZXREaWN0T3B0aW9ucyhmaWVsZEtleSkgewogICAgICBjb25zdCBkaWN0TWFwID0gewogICAgICAgIHN5c3RlbVR5cGU6ICdzeXN0ZW1fdHlwZScsCiAgICAgICAgY29uc3RydWN0OiAnY29uc3RydWN0X3R5cGUnLAogICAgICAgIGxvZ2luVHlwZTogJ2FwcF9sb2dpbl90eXBlJywKICAgICAgICB0ZWNobmljYWw6ICdhcHBfdGVjaG5pY2FsJywKICAgICAgICBkZXBsb3k6ICdhcHBfZGVwbG95JywKICAgICAgICBzdGF0ZTogJ2Fzc2V0X3N0YXRlJywKICAgICAgICBwcm90ZWN0R3JhZGU6ICdwcm90ZWN0aW9uX2dyYWRlJywKICAgICAgICBldmFsdWF0aW9uUmVzdWx0czogJ2V2YWx1YXRpb25fcmVzdWx0cycsCiAgICAgICAgZXZhbHVhdGlvblN0YXR1czogJ2V2YWx1YXRpb25fc3RhdHVzJywKICAgICAgICBod0lzVHJ1ZVNodXREb3duOiAnaHdfaXNfdHJ1ZV9zaHV0X2Rvd24nLAogICAgICAgIGNvdmVyQXJlYTogJ2NvdmVyX2FyZWEnCiAgICAgIH07CiAgICAgIHJldHVybiB0aGlzLmRpY3QudHlwZVtkaWN0TWFwW2ZpZWxkS2V5XV0gfHwgW107CiAgICB9LAoKICAgIGdldEFsbFNlcnZlckxpc3QoKSB7CiAgICAgIGxpc3RBbGxPdmVydmlldyh7ImFzc2V0Q2xhc3MiOiA0fSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuc2VydmVyT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KQogICAgfSwKICAgIC8qKiDliJ3lp4vljJYgKi8KICAgIGFzeW5jIGluaXQoKSB7CiAgICAgIC8vIGxldCBwYXJhbXMgPSB0aGlzLiRyb3V0ZS5xdWVyeTsKICAgICAgaWYgKHRoaXMuYXNzZXRJZCkgewogICAgICAgIGF3YWl0IGdldEFwcGxpY2F0aW9uKHRoaXMuYXNzZXRJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAvLyDojrflj5blupTnlKjkv6Hmga/or6bmg4UKICAgICAgICAgIHRoaXMuZm9ybS5hc3NldElkID0gdGhpcy5hc3NldElkOwogICAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YS5hcHBsaWNhdGlvblZPOwogICAgICAgICAgd2FpdEZvclZhbHVlKCgpID0+IGdldFZhbEZyb21PYmplY3QoJ3NpdGUnLCB0aGlzLiRyZWZzLCBudWxsKSkudGhlbihzaXRlID0+IHsKICAgICAgICAgICAgaWYoIXNpdGUpewogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgICBpZihzaXRlIGluc3RhbmNlb2YgQXJyYXkpewogICAgICAgICAgICAgIHNpdGUuZm9yRWFjaChpdGVtID0+IGl0ZW0uZ2V0TGlzdCgpKTsKICAgICAgICAgICAgfWVsc2UgewogICAgICAgICAgICAgIHNpdGUuZ2V0TGlzdCgpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgICAvLyDojrflj5bkuJrliqHkv6Hmga/or6bmg4UKICAgICAgICAgIHRoaXMuYnVzaW5lc3NGb3JtLmFzc2V0SWQgPSB0aGlzLmFzc2V0SWQ7CiAgICAgICAgICB0aGlzLmJ1c2luZXNzRm9ybSA9IHJlc3BvbnNlLmRhdGEudGJsQnVzaW5lc3NBcHBsaWNhdGlvbjsKICAgICAgICAgIHRoaXMuYnVzaW5lc3NGb3JtLnVzZXJOdW1zID0gdGhpcy5idXNpbmVzc0Zvcm0udXNlck51bXMgIT09IG51bGwgPyB0aGlzLmJ1c2luZXNzRm9ybS51c2VyTnVtcyArICcnIDogJyc7CiAgICAgICAgICB0aGlzLmJ1c2luZXNzRm9ybS5ldmVyeWRheVZpc2l0TnVtcyA9IHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5VmlzaXROdW1zICE9PSBudWxsID8gdGhpcy5idXNpbmVzc0Zvcm0uZXZlcnlkYXlWaXNpdE51bXMgKyAnJyA6ICcnOwogICAgICAgICAgdGhpcy5idXNpbmVzc0Zvcm0uZXZlcnlkYXlBY3RpdmVOdW1zID0gdGhpcy5idXNpbmVzc0Zvcm0uZXZlcnlkYXlBY3RpdmVOdW1zICE9PSBudWxsID8gdGhpcy5idXNpbmVzc0Zvcm0uZXZlcnlkYXlBY3RpdmVOdW1zICsgJycgOiAnJzsKICAgICAgICAgIHRoaXMuZnVuY3Rpb25TdGF0ZUxpc3QgPSByZXNwb25zZS5kYXRhLnRibEJ1c2luZXNzQXBwbGljYXRpb24udGJsTWFwcGVyTGlzdCB8fCBbe30sIHt9LCB7fV07CiAgICAgICAgICBpZiAodGhpcy5mdW5jdGlvblN0YXRlTGlzdC5sZW5ndGggPCAzKSB7CiAgICAgICAgICAgIGxldCBpID0gMDsKICAgICAgICAgICAgd2hpbGUgKGkgPCAzIC0gdGhpcy5mdW5jdGlvblN0YXRlTGlzdC5sZW5ndGgpIHsKICAgICAgICAgICAgICB0aGlzLmZ1bmN0aW9uU3RhdGVMaXN0LnB1c2goe30pOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7CiAgICAgICAgICB0aGlzLmFmdGVySW5pdCA9IHRydWU7CiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmFmdGVySW5pdCA9IHRydWU7CiAgICAgIH0KICAgIH0sCgoKICAgIC8qKiDooajljZXph43nva4gKi8KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgYXNzZXRJZDogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0Q29kZTogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0TmFtZTogdW5kZWZpbmVkLAogICAgICAgIHNvZnR3YXJlVmVyc2lvbjogdW5kZWZpbmVkLAogICAgICAgIGRlZ3JlZUltcG9ydGFuY2U6IHVuZGVmaW5lZCwKICAgICAgICBtYW5hZ2VyOiB1bmRlZmluZWQsCiAgICAgICAgZG9tYWluVXJsOiB1bmRlZmluZWQsCiAgICAgICAgc3lzdGVtVHlwZTogdW5kZWZpbmVkLAogICAgICAgIHBob25lOiB1bmRlZmluZWQsCiAgICAgICAgYXNzZXRUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgYXNzZXRUeXBlRGVzYzogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0Q2xhc3M6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldENsYXNzRGVzYzogdW5kZWZpbmVkLAogICAgICAgIGNvbnN0cnVjdDogdW5kZWZpbmVkLAogICAgICAgIG5ldFR5cGU6IHVuZGVmaW5lZCwKICAgICAgICBhcHBUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgc2VydmljZUdyb3VwOiB1bmRlZmluZWQsCiAgICAgICAgZnJlcXVlbmN5OiB1bmRlZmluZWQsCiAgICAgICAgdXNhZ2VDb3VudDogdW5kZWZpbmVkLAogICAgICAgIHVzZXJTY2FsZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJPYmplY3Q6IHVuZGVmaW5lZCwKICAgICAgICB1cmw6IHVuZGVmaW5lZCwKICAgICAgICBpcGQ6IHVuZGVmaW5lZCwKICAgICAgICB0ZWNobmljYWw6IHVuZGVmaW5lZCwKICAgICAgICBkZXBsb3k6IHVuZGVmaW5lZCwKICAgICAgICBzdG9yYWdlOiB1bmRlZmluZWQsCiAgICAgICAgbmV0ZW52OiB1bmRlZmluZWQsCiAgICAgICAgaXNrZXk6IHVuZGVmaW5lZCwKICAgICAgICBkYXRhbnVtOiB1bmRlZmluZWQsCiAgICAgICAgaXNiYXNlOiAiMCIsCiAgICAgICAgaXNsaW5rOiB1bmRlZmluZWQsCiAgICAgICAgaXNoYXJlOiB1bmRlZmluZWQsCiAgICAgICAgaXNsb2c6IHVuZGVmaW5lZCwKICAgICAgICBpc3BsYW46IHVuZGVmaW5lZCwKICAgICAgICBpc2FkYXB0OiB1bmRlZmluZWQsCiAgICAgICAgaXNjaXBoZXI6IHVuZGVmaW5lZCwKICAgICAgICBhZGFwdERhdGU6IHVuZGVmaW5lZCwKICAgICAgICBjaXBoZXJEYXRlOiB1bmRlZmluZWQsCiAgICAgICAgZnVuY3Rpb246IHVuZGVmaW5lZCwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwKICAgICAgICB1c2VySWQ6IHVuZGVmaW5lZCwKICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZCwKICAgICAgICBvcmduSWQ6IHVuZGVmaW5lZCwKICAgICAgICB2ZW5kb3JzOiB1bmRlZmluZWQsCiAgICAgICAgdXBUaW1lOiB1bmRlZmluZWQsCiAgICAgICAgZHdpZDogdW5kZWZpbmVkLAogICAgICAgIGNvbnRhY3RvcjogdW5kZWZpbmVkLAogICAgICAgIGRvbWFpbklkOiB1bmRlZmluZWQsCiAgICAgICAgbmV0U2NhbGU6IHVuZGVmaW5lZCwKICAgICAgICBuZXRUb3BvOiB1bmRlZmluZWQsCiAgICAgICAgbmV0TWVtbzogdW5kZWZpbmVkLAogICAgICAgIHRhZ3M6ICIiLAogICAgICAgIGxpbmtzOiBbXSwKICAgICAgICBlaWRzOiBbXSwKICAgICAgfTsKICAgICAgdGhpcy5idXNpbmVzc0Zvcm0gPSB7CiAgICAgICAgc3lzQnVzaW5lc3NTdGF0ZTogdW5kZWZpbmVkLAogICAgICAgIHVzZXJOdW1zOiB1bmRlZmluZWQsCiAgICAgICAgZXZlcnlkYXlWaXNpdE51bXM6IHVuZGVmaW5lZCwKICAgICAgICBldmVyeWRheUFjdGl2ZU51bXM6IHVuZGVmaW5lZCwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgICAgdGhpcy5yZXNldEZvcm0oImJ1c2luZXNzRm9ybSIpOwogICAgfSwKCiAgICBzZXJ2ZXJTZWxlY3QoZGF0YSkgewogICAgICBpZiAoZGF0YSkgewogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdhc3NvY2lhdGlvblNlcnZlcicsIGRhdGEubWFwKGl0ZW0gPT4gaXRlbS5zZXJ2ZXJJZCkpCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOafpeivouaJgOWxnumDqOmXqCAqLwogICAgZ2V0RGVwdFRyZWUoKSB7CiAgICAgIGlmICh0aGlzLiRlZGl0YWJsZS52YWx1ZSkgewogICAgICAgIGdldEFsbERlcHRUcmVlKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gZmxhdHRlblRyZWVUb0FycmF5KHJlc3BvbnNlLmRhdGEpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIGRlcHRUcmVlU2VsZWN0KCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gZmxhdHRlblRyZWVUb0FycmF5KHJlc3BvbnNlLmRhdGEpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAoKICAgIC8vICDmn6Xor6LmiYDmnInotKPku7vkurov55S16K+dCiAgICBnZXRNYW5hZ2VyTGlzdCgpIHsKICAgICAgbGlzdFVzZXIoewogICAgICAgIGlzQXNjOiAnZGVzYycsCiAgICAgICAgb3JkZXJCeUNvbHVtbjogJ2NyZWF0ZVRpbWUnLAogICAgICAgIGlzQWxsRGF0YTogdHJ1ZSwKICAgICAgICB1c2VyTmFtZTogbnVsbCwKICAgICAgICBuaWNrTmFtZTogbnVsbCwKICAgICAgICBwaG9uZW51bWJlcjogbnVsbCwKICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tYW5hZ2VyRGF0YSA9IHJlc3BvbnNlLnJvd3M7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKiog6I635Y+W5Li76YOo572y572R57ucICovCiAgICBnZXROZXR3b3JrRG9tYWluVHJlZSgpIHsKICAgICAgbGlzdERvbWFpbigpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubmV0d29ya0RvbWFpbk9wdGlvbnMgPSByZXNwb25zZS5kYXRhCiAgICAgIH0pOwogICAgfSwKCiAgICAvKiDojrflj5blvIDlj5HlkIjkvZzkvIHkuJogKi8KICAgIGdldFZlbmRvcnNEYXRhKCkgewogICAgICBsaXN0VmVuZG9yQnlBcHBsaWNhdGlvbih7CiAgICAgICAgYXBwbGljYXRpb25JZDogdGhpcy5hc3NldElkLAogICAgICAgIGFwcGxpY2F0aW9uQ29kZTogdGhpcy5mb3JtLnZlbmRvcnMsCiAgICAgICAgaXNBc2M6ICdkZXNjJywKICAgICAgICBvcmRlckJ5Q29sdW1uOiBudWxsLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHZlbmRvckNvZGU6IG51bGwsCiAgICAgICAgdmVuZG9yTmFtZTogbnVsbCwKICAgICAgICB2ZW5kb3JNYW5hZ2VOYW1lOiBudWxsLAogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnZlbmRvcnNEYXRhID0gcmVzcG9uc2Uucm93czsKICAgICAgfSk7CiAgICB9CiAgfSwKfQo="}, {"version": 3, "sources": ["OperationSystemDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "OperationSystemDetails.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<!--业务系统详情-->\n<template>\n  <div class=\"customForm-container\" style=\"height: 65vh\">\n    <template v-for=\"group in assetList\">\n      <div :key=\"group.formName\" style=\"margin-bottom: 20px;\">\n        <div class=\"my-title\">\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\">\n          <img v-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '用户规模'\" src=\"@/assets/images/application/yhgm.png\" alt=\"\">\n          <img v-if=\"group.formName === '业务描述'\" src=\"@/assets/images/application/ywms.png\" alt=\"\">\n          <img v-if=\"group.formName === '功能模块'\" src=\"@/assets/images/application/gnmk.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\">\n          {{ group.formName }}\n        </div>\n        <template v-if=\"group.formName === '外部连接信息'\">\n          <ApplicationLink\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            v-model=\"form.links\"/>\n        </template>\n        <template v-else-if=\"group.formName === '运营维护情况'\">\n          <ApplicationSite\n            ref=\"site\"\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            :value.sync=\"form.eids\"\n            :asset-id=\"form.assetId\"\n            multiple/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装服务器环境'\">\n          <serverEV\n            class=\"my-form\"\n            ref=\"serverEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"\n            :data-list=\"currentAssociationServer\"\n            @selected=\"serverSelect\"\n            v-if=\"afterInit\"/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装数据库环境'\">\n          <dateEV\n            class=\"my-form\"\n            ref=\"dateEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联网络设备'\">\n          <network-e-v\n            class=\"my-form\"\n            ref=\"networkEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联安全设备'\">\n          <safeEV\n            class=\"my-form\"\n            ref=\"safeEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '功能模块'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item :span=\"3\" v-for=\"(item, index) in functionStateList\" :key=\"index\">\n              <div style=\"display: flex; justify-content: space-around\">\n                <div>{{ item.moduleName }}</div>\n                <div>{{ item.moduleDesc }}</div>\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '用户规模'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === businessForm[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n              <template v-else-if=\"field.fieldKey === 'serviceGroup'\">\n                <div class=\"tag-group\">\n                  <template v-if=\"processedServiceGroups.length > 0\">\n                    <span v-for=\"(label, index) in processedServiceGroups\" :key=\"index\">\n                      {{ label }}\n                    </span>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择</span>\n                </div>\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '业务描述'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"businessForm[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              v-if=\"shouldShowField(field)\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n\n              <!-- 上传类型字段 -->\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n\n              <!-- 特殊字段：关联服务器 -->\n              <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                <div class=\"server-display\">\n                  <div v-for=\"id in form.associationServer\" :key=\"id\" class=\"server-item\">\n                    <span>{{ getServerName(id) }}</span>\n                  </div>\n                </div>\n              </template>\n\n              <!-- 特殊字段：责任人 -->\n              <template v-else-if=\"field.fieldKey === 'manager'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedManagerList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedManagerList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}（{{ user.phone }}）\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择责任人</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：单位 -->\n              <template v-else-if=\"field.fieldKey === 'deptId'\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}</span>\n              </template>\n\n              <!-- 特殊字段：主部署网络 -->\n              <template v-else-if=\"field.fieldKey === 'domainId'\">\n                <span\n                  v-for=\"(item, index) in networkDomainOptions\"\n                  :key=\"item.domainId\"\n                  v-if=\"item.domainId === form.domainId\"\n                >{{ item.domainName }}</span>\n              </template>\n\n              <!-- 特殊字段：开发合作企业 -->\n              <template v-else-if=\"field.fieldKey === 'vendor'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedVendorsList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedVendorsList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择开发合作企业</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：标签 -->\n              <template v-else-if=\"field.fieldKey === 'tags'\">\n                <template v-if=\"(form.tags || '').split(',').filter(t => t).length > 0\">\n                  <el-tag\n                    v-for=\"(tag,index) in (form.tags || '').split(',')\"\n                    :key=\"index\"\n                    closable\n                    size=\"small\"\n                    v-show=\"tag\"\n                  >\n                    {{ tag }}\n                  </el-tag>\n                </template>\n                <span v-else class=\"gray-text\">暂无标签</span>\n              </template>\n\n              <!-- 下拉选择类型字段 -->\n              <template v-else-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === form[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n\n              <!-- 默认文本显示 -->\n              <template v-else>\n                <span>{{ getFieldValue(field) }}</span>\n              </template>\n            </el-descriptions-item>\n\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {flattenTreeData, flattenTreeToArray, getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\nimport {getAllDeptTree, deptTreeSelect, listUser} from \"@/api/system/user\"\nimport {listDomain} from \"@/api/dict/domain\";\nimport dayjs from \"dayjs\";\n\nexport default {\n  name: \"OperationSystemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      deptOptions: [],\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      managerData: [],\n      networkDomainOptions: [],\n      vendorsData: [],\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      uploadType: ['waitingInsuranceFilingScan', 'evaluationReport', 'netTopo', 'operateHandbook'],\n      selectType: ['systemType', 'construct', 'loginType', 'technical', 'deploy', 'state', 'protectGrade', 'evaluationResults', 'evaluationStatus', 'coverArea'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.getDeptTree();\n    this.getManagerList();\n    this.getNetworkDomainTree();\n    this.getVendorsData();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  computed: {\n    // 获取服务器名称映射\n    getServerName() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.assetName || ''\n    },\n    // 获取服务器IP映射\n    getServerIp() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.ip || ''\n    },\n    processedManagerList() {\n      // 去重\n      const ids = [...new Set(\n        (this.form.manager || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.managerData.find(u =>\n          Number(u.userId) === Number(id)\n        );\n        return {\n          id,\n          name: user?.nickName || '未知用户',\n          phone: user?.phonenumber || ''\n        };\n      });\n    },\n    processedVendorsList() {\n      const ids = [...new Set(\n        (this.form.vendors || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.vendorsData.find(u =>\n          Number(u.id) === Number(id)\n        );\n        return {\n          id,\n          name: user?.vendorName || '未知用户',\n        };\n      });\n    },\n    processedServiceGroups() {\n      if (!this.businessForm.serviceGroup) return []\n      return this.businessForm.serviceGroup.split(',')\n        .map(val => this.dict.type['serve_group'].find(d => d.value === val)?.label || val)\n    }\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if (Object.keys(item).length > 0) {\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    },\n    'form.systemType': {\n      handler(newVal, oldVal) {\n        if (newVal) {\n          this.form.systemType = newVal.toString();\n        }\n      }\n    },\n  },\n  methods: {\n    getFieldValue(field) {\n      // 其他基本信息字段格式化\n      let filterArr = ['isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'iskey', 'isOpenNetwork']\n      if (filterArr.includes(field.fieldKey)) {\n        return this.form[field.fieldKey] === 'Y' ? '是' : '否';\n      }\n      if(field.fieldKey === 'hwIsTrueShutDown'){\n        return this.dict.type.hw_is_true_shut_down.find(d => d.value === this.form[field.fieldKey])?.label || this.form[field.fieldKey];\n      }\n      if(field.fieldKey === 'uodTime'){\n        return dayjs(this.form[field.fieldKey]).format('YYYY-MM-DD HH:mm:ss')\n      }\n      return this.form[field.fieldKey];\n    },\n\n    getFieldSpan(field) {\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];\n      if (fullSpanFields.includes(field.fieldKey)) return 3;\n      // 其他字段默认占8列\n      return 1;\n    },\n    // 判断字段是否显示\n    shouldShowField(field) {\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType === '12';\n      }\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down',\n        coverArea: 'cover_area'\n      };\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    getAllServerList() {\n      listAllOverview({\"assetClass\": 4}).then(res => {\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(() => {\n          this.afterInit = true;\n        })\n      } else {\n        this.afterInit = true;\n      }\n    },\n\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n\n    serverSelect(data) {\n      if (data) {\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      if (this.$editable.value) {\n        getAllDeptTree().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      } else {\n        deptTreeSelect().then(response => {\n          this.deptOptions = flattenTreeToArray(response.data);\n        });\n      }\n    },\n\n    //  查询所有责任人/电话\n    getManagerList() {\n      listUser({\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        isAllData: true,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      }).then(response => {\n        this.managerData = response.rows;\n      });\n    },\n\n    /** 获取主部署网络 */\n    getNetworkDomainTree() {\n      listDomain().then(response => {\n        this.networkDomainOptions = response.data\n      });\n    },\n\n    /* 获取开发合作企业 */\n    getVendorsData() {\n      listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        isAsc: 'desc',\n        orderByColumn: null,\n        pageNum: 1,\n        pageSize: 10,\n        vendorCode: null,\n        vendorName: null,\n        vendorManageName: null,\n      }).then(response => {\n        this.vendorsData = response.rows;\n      });\n    }\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.server-display {\n  line-height: 1.8;\n  display: flex;\n}\n\n.server-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 5px;\n}\n</style>\n"]}]}