{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\monitor\\deviceConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\monitor\\deviceConfig\\index.vue", "mtime": 1755138396950}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RGV2aWNlQ29uZmlnLA0KICBnZXREZXZpY2VDb25maWcsDQogIGRlbERldmljZUNvbmZpZywNCiAgYWRkRGV2aWNlQ29uZmlnLA0KICB1cGRhdGVEZXZpY2VDb25maWcsDQogIHVwZGF0ZURldmljZUNvbmZpZ1N0YXR1cw0KfSBmcm9tICdAL2FwaS9mZnNhZmUvZGV2aWNlQ29uZmlnJw0KaW1wb3J0IHsgY2hlY2tQZXJtaSB9IGZyb20gJ0AvdXRpbHMvcGVybWlzc2lvbicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnRGV2aWNlQ29uZmlnJywNCiAgZGF0YSgpIHsNCiAgICAvLyDoh6rlrprkuYlKU09O6aqM6K+B5Ye95pWwDQogICAgY29uc3QgdmFsaWRhdGVKc29uID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKHZhbHVlID09PSAnJykgew0KICAgICAgICByZXR1cm4gY2FsbGJhY2sobmV3IEVycm9yKCdKU09O5LiN6IO95Li656m6JykpDQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIDEuIOWwneivleino+aekEpTT04NCiAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZSh2YWx1ZSkNCg0KICAgICAgICAvLyAyLiDmoKHpqozmmK/lkKbkuLrlr7nosaHvvIjlj6/pgInvvIkNCiAgICAgICAgaWYgKHR5cGVvZiBwYXJzZWQgIT09ICdvYmplY3QnIHx8IHBhcnNlZCA9PT0gbnVsbCkgew0KICAgICAgICAgIHJldHVybiBjYWxsYmFjayhuZXcgRXJyb3IoJ0pTT07lv4XpobvmmK/lr7nosaHmiJbmlbDnu4QnKSkNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIDMuIOagoemqjOepuuWvueixoS/nqbrmlbDnu4TvvIjlj6/pgInvvIkNCiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkKSAmJiBwYXJzZWQubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgcmV0dXJuIGNhbGxiYWNrKG5ldyBFcnJvcign5pWw57uE5LiN6IO95Li656m6JykpDQogICAgICAgIH0NCg0KICAgICAgICBpZiAoIUFycmF5LmlzQXJyYXkocGFyc2VkKSAmJiBPYmplY3Qua2V5cyhwYXJzZWQpLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHJldHVybiBjYWxsYmFjayhuZXcgRXJyb3IoJ+WvueixoeS4jeiDveS4uuepuicpKQ0KICAgICAgICB9DQoNCiAgICAgICAgY2FsbGJhY2soKQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAvLyA0LiDmj5Dlj5ZKU09O6Kej5p6Q6ZSZ6K+v55qE5YW35L2T5L2N572uDQogICAgICAgIGNvbnN0IGVycm9yUG9zID0gdGhpcy5mb3JtYXRKc29uRXJyb3IoZS5tZXNzYWdlKQ0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoYOaXoOaViOeahEpTT07moLzlvI86ICR7ZXJyb3JQb3MgfHwgZS5tZXNzYWdlfWApKQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIHJldHVybiB7DQogICAgICAvL+eUqOaIt+mAieaLqQ0KICAgICAgdXNlckRpYWxvZzogZmFsc2UsDQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6K6+5aSH5o6l5YWl6YWN572u6KGo5qC85pWw5o2uDQogICAgICBkZXZpY2VDb25maWdMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgaXNBc2M6IHVuZGVmaW5lZCwNCiAgICAgICAgb3JkZXJCeUNvbHVtbjogdW5kZWZpbmVkLA0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGRldmljZU5hbWU6IG51bGwsDQogICAgICAgIGRldmljZUlwOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBkZXZpY2VOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+iuvuWkh+WQjeensOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGRldmljZUlwOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+iuvuWkh0lQ5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgZGV2aWNlUGFyYW1zOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+iuvuWkh+aOpeWFpeWPguaVsOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB2YWxpZGF0ZUpzb24sIHRyaWdnZXI6IFsnYmx1ciddIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGNvbHVtbnM6IFsNCiAgICAgICAgeyBrZXk6IDAsIGxhYmVsOiAn6K6+5aSH5ZCN56ewJywgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogMSwgbGFiZWw6ICforr7lpIdJUCcsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDIsIGxhYmVsOiAn6K6+5aSH5o6l5YWl5Y+C5pWwJywgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogMywgbGFiZWw6ICfkv67mlLnml7bpl7QnLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA0LCBsYWJlbDogJ+eKtuaAgScsIHZpc2libGU6IHRydWUgfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgY2hlY2tQZXJtaSwNCiAgICAvL+iOt+WPluS6uuWRmOaVsOaNrg0KICAgIGdldFRhYmxlRGF0YSgpIHsNCiAgICB9LA0KICAgIC8v5YWz6Zet55So5oi356qX5Y+jDQogICAgY2xvc2VVc2VyRGlhbG9nKCkgew0KICAgICAgdGhpcy51c2VyRGlhbG9nID0gZmFsc2UNCiAgICB9LA0KICAgIC8v5omT5byA55So5oi36YCJ5oup56qX5Y+jDQogICAgc2hvd1VzZXJEaWFsb2codmFsKSB7DQogICAgICB0aGlzLmRpYWxvZ05hbWUgPSB2YWwNCiAgICAgIHRoaXMudXNlckRpYWxvZyA9IHRydWUNCiAgICB9LA0KICAgIC8v5o6S5bqPDQogICAgc29ydENoYW5nZShjb2x1bW4sIHByb3AsIG9yZGVyKSB7DQogICAgICBpZiAoY29sdW1uLm9yZGVyICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc0FzYyA9ICdkZXNjJw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc0FzYyA9ICdhc2MnDQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9yZGVyQnlDb2x1bW4gPSBjb2x1bW4ucHJvcA0KICAgICAgdGhpcy5nZXRMaXN0KHRoaXMucXVlcnlQYXJhbXMpDQogICAgfSwNCiAgICAvKiog5p+l6K+i6K6+5aSH5o6l5YWl6YWN572u5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3REZXZpY2VDb25maWcodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZGV2aWNlQ29uZmlnTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBkZXZpY2VOYW1lOiBudWxsLA0KICAgICAgICBkZXZpY2VJcDogbnVsbCwNCiAgICAgICAgZGV2aWNlUGFyYW1zOiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHN0YXR1czogbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oJ2Zvcm0nKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oJ3F1ZXJ5Rm9ybScpDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICfmt7vliqDorr7lpIfmjqXlhaXphY3nva4nDQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0RGV2aWNlQ29uZmlnKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgICAgIHRoaXMudGl0bGUgPSAn5L+u5pS56K6+5aSH5o6l5YWl6YWN572uJw0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVEZXZpY2VDb25maWcodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5L+u5pS55oiQ5YqfJykNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERldmljZUNvbmZpZyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmlrDlop7miJDlip8nKQ0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTorr7lpIfmjqXlhaXphY3nva7nvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbERldmljZUNvbmZpZyhpZHMpDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnYXBpL2RldmljZUNvbmZpZy9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGBkZXZpY2VDb25maWdfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICBjaGFuZ2VTdGF0dXMocm93KXsNCiAgICAgIGNvbnN0IHRleHQgPSByb3cuc3RhdHVzID09PSAxID8gJ+WQr+eUqCcgOiAn5YGc55SoJw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJyIiJyArIHJvdy5kZXZpY2VOYW1lICsgJyLlkJfvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICAgIHN0YXR1czogcm93LnN0YXR1cw0KICAgICAgICB9DQogICAgICAgIHJldHVybiB1cGRhdGVEZXZpY2VDb25maWdTdGF0dXMoZGF0YSkNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAn5oiQ5YqfJykNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09IDAgPyAxIDogMA0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOagvOW8j+WMlkpTT07plJnor6/mtojmga8NCiAgICBmb3JtYXRKc29uRXJyb3IobWVzc2FnZSkgew0KICAgICAgY29uc3QgbWF0Y2hlcyA9IG1lc3NhZ2UubWF0Y2goL3Bvc2l0aW9uIChcZCspLyk7DQogICAgICBpZiAobWF0Y2hlcyAmJiBtYXRjaGVzWzFdKSB7DQogICAgICAgIGNvbnN0IHBvc2l0aW9uID0gcGFyc2VJbnQobWF0Y2hlc1sxXSwgMTApOw0KICAgICAgICByZXR1cm4gYOmUmeivr+S9jee9rjog56ysICR7cG9zaXRpb259IOS4quWtl+espuWkhGA7DQogICAgICB9DQogICAgICByZXR1cm4gbWVzc2FnZS5yZXBsYWNlKCdKU09OLnBhcnNlOicsICcnKTsNCiAgICB9LA0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/deviceConfig", "sourcesContent": ["<template>\r\n  <div class=\"custom-container\">\r\n    <div class=\"custom-content-container-right\">\r\n      <div class=\"custom-content-search-box\" v-show=\"showSearch\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"70px\"\r\n                 label-position=\"right\"\r\n        >\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"设备名称\" prop=\"deviceName\">\r\n                <el-input v-model=\"queryParams.deviceName\" placeholder=\"请输入设备名称\" clearable\r\n                          @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"设备IP\" prop=\"deviceIp\">\r\n                <el-input v-model=\"queryParams.deviceIp\" placeholder=\"请输入设备IP\" clearable\r\n                          @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item class=\"custom-search-btn\">\r\n                <el-button\r\n                  class=\"btn1\"\r\n                  size=\"small\"\r\n                  @click=\"handleQuery\"\r\n                >查询\r\n                </el-button>\r\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"custom-content-container\">\r\n        <div class=\"common-header\">\r\n          <div><span class=\"common-head-title\">设备接入配置</span></div>\r\n          <div class=\"common-head-right\">\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button type=\"primary\" size=\"small\" @click=\"handleAdd\" v-hasPermi=\"['api:deviceConfig:add']\">\r\n                  新增\r\n                </el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n        <div class=\"tableContainer\">\r\n          <el-table height=\"100%\" v-loading=\"loading\" :data=\"deviceConfigList\" @selection-change=\"handleSelectionChange\"\r\n                    @sort-change=\"sortChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\"/>\r\n            <el-table-column label=\"设备名称\" align=\"center\" prop=\"deviceName\" v-if=\"columns[0].visible\" />\r\n            <el-table-column label=\"设备IP\" align=\"center\" prop=\"deviceIp\" v-if=\"columns[1].visible\" />\r\n            <el-table-column label=\"设备接入参数\" align=\"center\" prop=\"deviceParams\" v-if=\"columns[2].visible\" min-width=\"220\" />\r\n            <el-table-column label=\"修改时间\" align=\"center\" prop=\"updateTime\" v-if=\"columns[3].visible\"\r\n                             width=\"200\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"状态\" align=\"center\" prop=\"status\" v-if=\"columns[4].visible\" width=\"160\">\r\n              <template slot-scope=\"scope\">\r\n                <el-switch v-model=\"scope.row.status\" :active-value=\"1\" :inactive-value=\"0\" active-color=\"#13ce66\" :disabled=\"!checkPermi(['api:deviceConfig:edit'])\" @change=\"changeStatus(scope.row)\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" fixed=\"right\" :show-overflow-tooltip=\"false\" width=\"160\"\r\n                             class-name=\"small-padding fixed-width\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\">详情</el-button>\r\n                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\"\r\n                           v-hasPermi=\"['api:deviceConfig:edit']\"\r\n                >编辑\r\n                </el-button>\r\n                <el-button size=\"mini\" type=\"text\"\r\n                           @click=\"handleDelete(scope.row)\" v-hasPermi=\"['api:deviceConfig:remove']\"\r\n                >删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n                    @pagination=\"getList\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <!-- 添加或修改事务管理对话框! -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-row>\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"设备名称\" prop=\"deviceName\">\r\n              <el-input v-model=\"form.deviceName\" placeholder=\"请输入设备名称\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"设备IP\" prop=\"deviceIp\">\r\n              <el-input v-model=\"form.deviceIp\" placeholder=\"请输入设备IP\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"接入参数\" prop=\"deviceParams\">\r\n              <el-input v-model=\"form.deviceParams\" type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 8}\" :maxlength=\"2000\" show-word-limit placeholder=\"请输入内容\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listDeviceConfig,\r\n  getDeviceConfig,\r\n  delDeviceConfig,\r\n  addDeviceConfig,\r\n  updateDeviceConfig,\r\n  updateDeviceConfigStatus\r\n} from '@/api/ffsafe/deviceConfig'\r\nimport { checkPermi } from '@/utils/permission'\r\n\r\nexport default {\r\n  name: 'DeviceConfig',\r\n  data() {\r\n    // 自定义JSON验证函数\r\n    const validateJson = (rule, value, callback) => {\r\n      if (value === '') {\r\n        return callback(new Error('JSON不能为空'))\r\n      }\r\n\r\n      try {\r\n        // 1. 尝试解析JSON\r\n        const parsed = JSON.parse(value)\r\n\r\n        // 2. 校验是否为对象（可选）\r\n        if (typeof parsed !== 'object' || parsed === null) {\r\n          return callback(new Error('JSON必须是对象或数组'))\r\n        }\r\n\r\n        // 3. 校验空对象/空数组（可选）\r\n        if (Array.isArray(parsed) && parsed.length === 0) {\r\n          return callback(new Error('数组不能为空'))\r\n        }\r\n\r\n        if (!Array.isArray(parsed) && Object.keys(parsed).length === 0) {\r\n          return callback(new Error('对象不能为空'))\r\n        }\r\n\r\n        callback()\r\n      } catch (e) {\r\n        // 4. 提取JSON解析错误的具体位置\r\n        const errorPos = this.formatJsonError(e.message)\r\n        callback(new Error(`无效的JSON格式: ${errorPos || e.message}`))\r\n      }\r\n    }\r\n\r\n    return {\r\n      //用户选择\r\n      userDialog: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 设备接入配置表格数据\r\n      deviceConfigList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        isAsc: undefined,\r\n        orderByColumn: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        deviceName: null,\r\n        deviceIp: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        deviceName: [\r\n          { required: true, message: '设备名称不能为空', trigger: 'blur' }\r\n        ],\r\n        deviceIp: [\r\n          { required: true, message: '设备IP不能为空', trigger: 'blur' }\r\n        ],\r\n        deviceParams: [\r\n          { required: true, message: '设备接入参数不能为空', trigger: 'blur' },\r\n          { validator: validateJson, trigger: ['blur'] }\r\n        ]\r\n      },\r\n      columns: [\r\n        { key: 0, label: '设备名称', visible: true },\r\n        { key: 1, label: '设备IP', visible: true },\r\n        { key: 2, label: '设备接入参数', visible: true },\r\n        { key: 3, label: '修改时间', visible: true },\r\n        { key: 4, label: '状态', visible: true }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n    //获取人员数据\r\n    getTableData() {\r\n    },\r\n    //关闭用户窗口\r\n    closeUserDialog() {\r\n      this.userDialog = false\r\n    },\r\n    //打开用户选择窗口\r\n    showUserDialog(val) {\r\n      this.dialogName = val\r\n      this.userDialog = true\r\n    },\r\n    //排序\r\n    sortChange(column, prop, order) {\r\n      if (column.order != null) {\r\n        this.queryParams.isAsc = 'desc'\r\n      } else {\r\n        this.queryParams.isAsc = 'asc'\r\n      }\r\n      this.queryParams.orderByColumn = column.prop\r\n      this.getList(this.queryParams)\r\n    },\r\n    /** 查询设备接入配置列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listDeviceConfig(this.queryParams).then(response => {\r\n        this.deviceConfigList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        deviceName: null,\r\n        deviceIp: null,\r\n        deviceParams: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        status: null\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加设备接入配置'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const id = row.id || this.ids\r\n      getDeviceConfig(id).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = '修改设备接入配置'\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDeviceConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess('修改成功')\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addDeviceConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess('新增成功')\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal.confirm('是否确认删除设备接入配置编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delDeviceConfig(ids)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('api/deviceConfig/export', {\r\n        ...this.queryParams\r\n      }, `deviceConfig_${new Date().getTime()}.xlsx`)\r\n    },\r\n    changeStatus(row){\r\n      const text = row.status === 1 ? '启用' : '停用'\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.deviceName + '\"吗？').then(() => {\r\n        const data = {\r\n          id: row.id,\r\n          status: row.status\r\n        }\r\n        return updateDeviceConfigStatus(data)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + '成功')\r\n      }).catch(() => {\r\n        row.status = row.status === 0 ? 1 : 0\r\n      })\r\n    },\r\n    // 格式化JSON错误消息\r\n    formatJsonError(message) {\r\n      const matches = message.match(/position (\\d+)/);\r\n      if (matches && matches[1]) {\r\n        const position = parseInt(matches[1], 10);\r\n        return `错误位置: 第 ${position} 个字符处`;\r\n      }\r\n      return message.replace('JSON.parse:', '');\r\n    },\r\n  }\r\n}\r\n</script>\r\n"]}]}