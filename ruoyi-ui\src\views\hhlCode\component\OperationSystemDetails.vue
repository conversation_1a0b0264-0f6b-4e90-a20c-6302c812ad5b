<!--业务系统详情-->
<template>
  <div class="customForm-container" style="height: 65vh">
    <template v-for="group in assetList">
      <div :key="group.formName" style="margin-bottom: 20px;">
        <div class="my-title">
          <img v-if="group.formName === '基本信息'" src="@/assets/images/application/baxx.png" alt="">
          <img v-if="group.formName === '备案信息'" src="@/assets/images/application/baxx.png" alt="">
          <img v-if="group.formName === '测评信息'" src="@/assets/images/application/cpxx.png" alt="">
          <img v-if="group.formName === '外部连接信息'" src="@/assets/images/application/wblj.png" alt="">
          <img v-if="group.formName === '拓扑结构信息'" src="@/assets/images/application/tpxx.png" alt="">
          <img v-if="group.formName === '运营维护情况'" src="@/assets/images/application/ywxx.png" alt="">
          <img v-if="group.formName === '其他基本信息'" src="@/assets/images/application/qtxx.png" alt="">
          <img v-if="group.formName === '用户规模'" src="@/assets/images/application/yhgm.png" alt="">
          <img v-if="group.formName === '业务描述'" src="@/assets/images/application/ywms.png" alt="">
          <img v-if="group.formName === '功能模块'" src="@/assets/images/application/gnmk.png" alt="">
          <img v-if="group.formName === '所安装服务器环境'" src="@/assets/images/application/fwq.png" alt="">
          <img v-if="group.formName === '所安装数据库环境'" src="@/assets/images/application/sjk.png" alt="">
          <img v-if="group.formName === '关联网络设备'" src="@/assets/images/application/wlsb.png" alt="">
          <img v-if="group.formName === '关联安全设备'" src="@/assets/images/application/aqsb.png" alt="">
          {{ group.formName }}
        </div>
        <template v-if="group.formName === '外部连接信息'">
          <ApplicationLink
            :fields="group.fieldsItems"
            :disabled="!$editable.value"
            v-model="form.links"/>
        </template>
        <template v-else-if="group.formName === '运营维护情况'">
          <ApplicationSite
            ref="site"
            :fields="group.fieldsItems"
            :disabled="!$editable.value"
            :value.sync="form.eids"
            :asset-id="form.assetId"
            multiple/>
        </template>
        <template v-else-if="group.formName === '所安装服务器环境'">
          <serverEV
            class="my-form"
            ref="serverEV"
            :fields="group.fieldsItems"
            :function-list.sync="functionStateList"
            :asset-id="assetId"
            :data-list="currentAssociationServer"
            @selected="serverSelect"
            v-if="afterInit"/>
        </template>
        <template v-else-if="group.formName === '所安装数据库环境'">
          <dateEV
            class="my-form"
            ref="dateEV"
            :fields="group.fieldsItems"
            :function-list.sync="functionStateList"
            :asset-id="assetId"/>
        </template>
        <template v-else-if="group.formName === '关联网络设备'">
          <network-e-v
            class="my-form"
            ref="networkEV"
            :fields="group.fieldsItems"
            :asset-id="assetId"/>
        </template>
        <template v-else-if="group.formName === '关联安全设备'">
          <safeEV
            class="my-form"
            ref="safeEV"
            :fields="group.fieldsItems"
            :asset-id="assetId"/>
        </template>
        <template v-else-if="group.formName === '功能模块'">
          <el-descriptions
            class="custom-column"
            direction="vertical"
            size="medium"
            :colon="false"
            label-class-name="custom-label-style"
            content-class-name="custom-content-style"
            :column="3">
            <el-descriptions-item :span="3" v-for="(item, index) in functionStateList" :key="index">
              <div style="display: flex; justify-content: space-around">
                <div>{{ item.moduleName }}</div>
                <div>{{ item.moduleDesc }}</div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template v-else-if="group.formName === '用户规模'">
          <el-descriptions
            class="custom-column"
            direction="vertical"
            size="medium"
            :colon="false"
            label-class-name="custom-label-style"
            content-class-name="custom-content-style"
            :column="3">
            <el-descriptions-item
              v-for="field in group.fieldsItems"
              :key="field.fieldKey"
              :label="field.fieldName"
              :span="getFieldSpan(field)">
              <template v-if="selectType.includes(field.fieldKey)">
                <span
                  v-for="item in getDictOptions(field.fieldKey)"
                  v-show="item.value === businessForm[field.fieldKey]"
                  :key="item.value">
                  {{ item.label }}
                </span>
              </template>
              <template v-else-if="field.fieldKey === 'serviceGroup'">
                <div class="tag-group">
                  <template v-if="processedServiceGroups.length > 0">
                    <span v-for="(label, index) in processedServiceGroups" :key="index">
                      {{ label }}
                    </span>
                  </template>
                  <span v-else class="gray-text">未选择</span>
                </div>
              </template>
              <template v-else>
                <span>{{ businessForm[field.fieldKey] }}</span>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template v-else-if="group.formName === '业务描述'">
          <el-descriptions
            class="custom-column"
            direction="vertical"
            size="medium"
            :colon="false"
            label-class-name="custom-label-style"
            content-class-name="custom-content-style"
            :column="3">
            <el-descriptions-item
              v-for="field in group.fieldsItems"
              :key="field.fieldKey"
              :label="field.fieldName"
              :span="getFieldSpan(field)">
              <template v-if="uploadType.includes(field.fieldKey)">
                <file-upload
                  :disUpload="!$editable.value"
                  v-model="businessForm[field.fieldKey]"
                  :limit="5"
                  :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                />
              </template>
              <template v-else>
                <span>{{ businessForm[field.fieldKey] }}</span>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template v-else>
          <el-descriptions
            class="custom-column"
            direction="vertical"
            size="medium"
            :colon="false"
            label-class-name="custom-label-style"
            content-class-name="custom-content-style"
            :column="3">
            <el-descriptions-item
              v-for="field in group.fieldsItems"
              v-if="shouldShowField(field)"
              :key="field.fieldKey"
              :label="field.fieldName"
              :span="getFieldSpan(field)">

              <!-- 上传类型字段 -->
              <template v-if="uploadType.includes(field.fieldKey)">
                <file-upload
                  :disUpload="!$editable.value"
                  v-model="form[field.fieldKey]"
                  :limit="5"
                  :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                />
              </template>

              <!-- 特殊字段：关联服务器 -->
              <template v-else-if="field.fieldKey === 'associationServer'">
                <div class="server-display">
                  <div v-for="id in form.associationServer" :key="id" class="server-item">
                    <span>{{ getServerName(id) }}</span>
                  </div>
                </div>
              </template>

              <!-- 特殊字段：责任人 -->
              <template v-else-if="field.fieldKey === 'manager'">
                <div class="manager-tags">
                  <template v-if="processedManagerList.length > 0">
                    <el-tag
                      v-for="user in processedManagerList"
                      :key="user.id"
                      size="small"
                    >
                      {{ user.name }}（{{ user.phone }}）
                    </el-tag>
                  </template>
                  <span v-else class="gray-text">未选择责任人</span>
                </div>
              </template>

              <!-- 特殊字段：单位 -->
              <template v-else-if="field.fieldKey === 'deptId'">
                <span
                  v-for="(item, index) in deptOptions"
                  :key="item.id"
                  v-if="item.id === form.deptId"
                >{{ item.label }}</span>
              </template>

              <!-- 特殊字段：主部署网络 -->
              <template v-else-if="field.fieldKey === 'domainId'">
                <span
                  v-for="(item, index) in networkDomainOptions"
                  :key="item.domainId"
                  v-if="item.domainId === form.domainId"
                >{{ item.domainName }}</span>
              </template>

              <!-- 特殊字段：开发合作企业 -->
              <template v-else-if="field.fieldKey === 'vendor'">
                <div class="manager-tags">
                  <template v-if="processedVendorsList.length > 0">
                    <el-tag
                      v-for="user in processedVendorsList"
                      :key="user.id"
                      size="small"
                    >
                      {{ user.name }}
                    </el-tag>
                  </template>
                  <span v-else class="gray-text">未选择开发合作企业</span>
                </div>
              </template>

              <!-- 特殊字段：标签 -->
              <template v-else-if="field.fieldKey === 'tags'">
                <template v-if="(form.tags || '').split(',').filter(t => t).length > 0">
                  <el-tag
                    v-for="(tag,index) in (form.tags || '').split(',')"
                    :key="index"
                    closable
                    size="small"
                    v-show="tag"
                  >
                    {{ tag }}
                  </el-tag>
                </template>
                <span v-else class="gray-text">暂无标签</span>
              </template>

              <!-- 下拉选择类型字段 -->
              <template v-else-if="selectType.includes(field.fieldKey)">
                <span
                  v-for="item in getDictOptions(field.fieldKey)"
                  v-show="item.value === form[field.fieldKey]"
                  :key="item.value">
                  {{ item.label }}
                </span>
              </template>

              <!-- 默认文本显示 -->
              <template v-else>
                <span>{{ getFieldValue(field) }}</span>
              </template>
            </el-descriptions-item>

          </el-descriptions>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import {getApplication} from "@/api/safe/application";
import ApplicationLink from '@/views/hhlCode/component/application/applicationLink';
import ApplicationSite from '@/views/hhlCode/component/application/applicationSite';
import UserSelect from '@/views/hhlCode/component/userSelect';
import DeptSelect from '@/views/components/select/deptSelect';
import NetworkSelect from '@/views/components/select/networkSelect';
import DynamicTag from '@/components/DynamicTag';
import VendorSelect2 from '@/views/components/select/vendorSelect2';
import DictSelect from '@/views/components/select/dictSelect';
import {flattenTreeData, flattenTreeToArray, getValFromObject} from "@/utils";
import {generateSecureUUID, waitForValue} from "@/utils/ruoyi";
import {listVendorByApplication} from "@/api/safe/vendor";
import serverEV from "@/views/hhlCode/component/application/applicationHardware/serverEV.vue";
import dateEV from "@/views/hhlCode/component/application/applicationHardware/dateEV.vue";
import networkEV from "@/views/hhlCode/component/application/applicationHardware/networkEV.vue";
import safeEV from "@/views/hhlCode/component/application/applicationHardware/safeEV.vue";
import overViewSelect from "@/views/components/select/overViewSelect.vue";
import {listAllOverview} from "@/api/safe/overview";
import EditServer from "@/views/safe/server/editServer.vue";
import {getAllDeptTree, deptTreeSelect, listUser} from "@/api/system/user"
import {listDomain} from "@/api/dict/domain";
import dayjs from "dayjs";

export default {
  name: "OperationSystemDetails",
  components: {
    EditServer,
    overViewSelect,
    safeEV,
    networkEV,
    dateEV,
    serverEV,
    ApplicationLink,
    ApplicationSite,
    UserSelect,
    DeptSelect,
    NetworkSelect,
    DictSelect,
    DynamicTag,
    VendorSelect2,
  },
  dicts: [
    'serve_group',
    'cover_area',
    'sys_yes_no',
    'app_net_scale',
    'construct_type',
    'system_type',
    'protection_grade',
    'asset_state',
    'app_login_type',
    'app_technical',
    'app_deploy',
    'app_storage',
    'evaluation_results',
    'evaluation_status',
    'is_open_network',
    'hw_is_true_shut_down'
  ],
  inject: {
    $editable: {
      default: {value: true},
    }
  },
  props: {
    assetId: {
      type: [String, Number],
      required: false,
      default: null,
    },
    changeId: Function,
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    assetList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      loading: false,
      collapseNames: ['1', '2', '3', '4', '5'],
      vendorsdata: '1',
      userdata: '1',
      functionStateList: [{}, {}, {}],
      // 基本信息表单参数
      form: {},
      // 业务信息表单参数
      businessForm: {
        delList: []
      },
      deptOptions: [],
      gv: getValFromObject,
      deployLocation: localStorage.getItem("dl"),
      managerLabel: '责任人/电话',
      managePlaceholder: '请选择责任人',
      managerData: [],
      networkDomainOptions: [],
      vendorsData: [],
      refs: {
        'networkEV': "所安装服务器环境",
        'safeEV': '所安装数据环境',
        'serverEV': '关联网络设备',
        'dateEV': "关联安全设备"
      },
      collapse: ['1', '2', '3', '4'],
      showAddServer: false,
      serverOptions: [],
      currentAssociationServer: [],
      afterInit: false,
      uploadType: ['waitingInsuranceFilingScan', 'evaluationReport', 'netTopo', 'operateHandbook'],
      selectType: ['systemType', 'construct', 'loginType', 'technical', 'deploy', 'state', 'protectGrade', 'evaluationResults', 'evaluationStatus', 'coverArea'],
    }
  },
  mounted() {
    this.getAllServerList();
    this.getDeptTree();
    this.getManagerList();
    this.getNetworkDomainTree();
    this.getVendorsData();
    this.$nextTick(() => {
      if (this.deployLocation === 'fair') {
        this.managerLabel = '责任民警/电话'
        this.managePlaceholder = '请选择责任民警'
      }
      this.reset();
      this.init()
    });
  },
  computed: {
    // 获取服务器名称映射
    getServerName() {
      return (id) => this.serverOptions.find(s => s.assetId === id)?.assetName || ''
    },
    // 获取服务器IP映射
    getServerIp() {
      return (id) => this.serverOptions.find(s => s.assetId === id)?.ip || ''
    },
    processedManagerList() {
      // 去重
      const ids = [...new Set(
        (this.form.manager || '')
          .split(',')
          .filter(Boolean)
      )];

      return ids.map(id => {
        const user = this.managerData.find(u =>
          Number(u.userId) === Number(id)
        );
        return {
          id,
          name: user?.nickName || '未知用户',
          phone: user?.phonenumber || ''
        };
      });
    },
    processedVendorsList() {
      const ids = [...new Set(
        (this.form.vendors || '')
          .split(',')
          .filter(Boolean)
      )];

      return ids.map(id => {
        const user = this.vendorsData.find(u =>
          Number(u.id) === Number(id)
        );
        return {
          id,
          name: user?.vendorName || '未知用户',
        };
      });
    },
    processedServiceGroups() {
      if (!this.businessForm.serviceGroup) return []
      return this.businessForm.serviceGroup.split(',')
        .map(val => this.dict.type['serve_group'].find(d => d.value === val)?.label || val)
    }
  },
  activated() {
    this.$nextTick(() => {
      this.reset();
      this.init()
    });
  },
  watch: {
    functionStateList: {
      handler(newVal, oldVal) {
        if (newVal && newVal.length > 0) {
          newVal.forEach((item, index) => {
            if (Object.keys(item).length > 0) {
              item.tempId = generateSecureUUID();
            }
          })
        }
      },
    },
    'form.systemType': {
      handler(newVal, oldVal) {
        if (newVal) {
          this.form.systemType = newVal.toString();
        }
      }
    },
  },
  methods: {
    getFieldValue(field) {
      // 其他基本信息字段格式化
      let filterArr = ['isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'iskey', 'isOpenNetwork']
      if (filterArr.includes(field.fieldKey)) {
        return this.form[field.fieldKey] === 'Y' ? '是' : '否';
      }
      if(field.fieldKey === 'hwIsTrueShutDown'){
        return this.dict.type.hw_is_true_shut_down.find(d => d.value === this.form[field.fieldKey])?.label || this.form[field.fieldKey];
      }
      if(field.fieldKey === 'uodTime'){
        return dayjs(this.form[field.fieldKey]).format('YYYY-MM-DD HH:mm:ss')
      }
      return this.form[field.fieldKey];
    },

    getFieldSpan(field) {
      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];
      if (fullSpanFields.includes(field.fieldKey)) return 3;
      // 其他字段默认占8列
      return 1;
    },
    // 判断字段是否显示
    shouldShowField(field) {
      if (field.fieldKey === 'otherSystemNotes') {
        return this.form.systemType === '12';
      }
      if (field.fieldKey === 'adaptDate') {
        return this.form.isadapt === 'Y';
      }
      if (field.fieldKey === 'cipherDate') {
        return this.form.iscipher === 'Y';
      }
      if (field.fieldKey === 'islink') {
        return this.deployLocation === 'fair';
      }
      return true;
    },

    getDictOptions(fieldKey) {
      const dictMap = {
        systemType: 'system_type',
        construct: 'construct_type',
        loginType: 'app_login_type',
        technical: 'app_technical',
        deploy: 'app_deploy',
        state: 'asset_state',
        protectGrade: 'protection_grade',
        evaluationResults: 'evaluation_results',
        evaluationStatus: 'evaluation_status',
        hwIsTrueShutDown: 'hw_is_true_shut_down',
        coverArea: 'cover_area'
      };
      return this.dict.type[dictMap[fieldKey]] || [];
    },

    getAllServerList() {
      listAllOverview({"assetClass": 4}).then(res => {
        this.serverOptions = res.data;
      })
    },
    /** 初始化 */
    async init() {
      // let params = this.$route.query;
      if (this.assetId) {
        await getApplication(this.assetId).then(response => {
          // 获取应用信息详情
          this.form.assetId = this.assetId;
          this.form = response.data.applicationVO;
          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {
            if(!site){
              return;
            }
            if(site instanceof Array){
              site.forEach(item => item.getList());
            }else {
              site.getList()
            }
          })
          // 获取业务信息详情
          this.businessForm.assetId = this.assetId;
          this.businessForm = response.data.tblBusinessApplication;
          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';
          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';
          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';
          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];
          if (this.functionStateList.length < 3) {
            let i = 0;
            while (i < 3 - this.functionStateList.length) {
              this.functionStateList.push({});
            }
          }
        }).finally(() => {
          this.afterInit = true;
        })
      } else {
        this.afterInit = true;
      }
    },


    /** 表单重置 */
    reset() {
      this.form = {
        assetId: undefined,
        assetCode: undefined,
        assetName: undefined,
        softwareVersion: undefined,
        degreeImportance: undefined,
        manager: undefined,
        domainUrl: undefined,
        systemType: undefined,
        phone: undefined,
        assetType: undefined,
        assetTypeDesc: undefined,
        assetClass: undefined,
        assetClassDesc: undefined,
        construct: undefined,
        netType: undefined,
        appType: undefined,
        serviceGroup: undefined,
        frequency: undefined,
        usageCount: undefined,
        userScale: undefined,
        userObject: undefined,
        url: undefined,
        ipd: undefined,
        technical: undefined,
        deploy: undefined,
        storage: undefined,
        netenv: undefined,
        iskey: undefined,
        datanum: undefined,
        isbase: "0",
        islink: undefined,
        ishare: undefined,
        islog: undefined,
        isplan: undefined,
        isadapt: undefined,
        iscipher: undefined,
        adaptDate: undefined,
        cipherDate: undefined,
        function: undefined,
        remark: undefined,
        userId: undefined,
        deptId: undefined,
        orgnId: undefined,
        vendors: undefined,
        upTime: undefined,
        dwid: undefined,
        contactor: undefined,
        domainId: undefined,
        netScale: undefined,
        netTopo: undefined,
        netMemo: undefined,
        tags: "",
        links: [],
        eids: [],
      };
      this.businessForm = {
        sysBusinessState: undefined,
        userNums: undefined,
        everydayVisitNums: undefined,
        everydayActiveNums: undefined,
      };
      this.resetForm("form");
      this.resetForm("businessForm");
    },

    serverSelect(data) {
      if (data) {
        this.$set(this.form, 'associationServer', data.map(item => item.serverId))
      }
    },

    /** 查询所属部门 */
    getDeptTree() {
      if (this.$editable.value) {
        getAllDeptTree().then(response => {
          this.deptOptions = flattenTreeToArray(response.data);
        });
      } else {
        deptTreeSelect().then(response => {
          this.deptOptions = flattenTreeToArray(response.data);
        });
      }
    },

    //  查询所有责任人/电话
    getManagerList() {
      listUser({
        isAsc: 'desc',
        orderByColumn: 'createTime',
        isAllData: true,
        userName: null,
        nickName: null,
        phonenumber: null,
      }).then(response => {
        this.managerData = response.rows;
      });
    },

    /** 获取主部署网络 */
    getNetworkDomainTree() {
      listDomain().then(response => {
        this.networkDomainOptions = response.data
      });
    },

    /* 获取开发合作企业 */
    getVendorsData() {
      listVendorByApplication({
        applicationId: this.assetId,
        applicationCode: this.form.vendors,
        isAsc: 'desc',
        orderByColumn: null,
        pageNum: 1,
        pageSize: 10,
        vendorCode: null,
        vendorName: null,
        vendorManageName: null,
      }).then(response => {
        this.vendorsData = response.rows;
      });
    }
  },
}
</script>

<style scoped lang="scss">
@import "@/assets/styles/customForm";

.server-display {
  line-height: 1.8;
  display: flex;
}

.server-item {
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}
</style>
