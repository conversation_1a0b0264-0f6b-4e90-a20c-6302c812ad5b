# 主机漏扫记录查询接口开发任务计划

## 项目概述

基于aqsoc-main项目中的FfsafeScantaskSummaryController，开发一个新的主机漏扫记录查询接口，支持任务名称和扫描目标的模糊查询，并返回存活主机数量和弱口令数量等统计信息。

## 需求规格

### 查询条件参数
- 任务名称（taskName）- 支持模糊查询
- 扫描目标（scanTarget）- 支持模糊查询  
- 保留原有的分页参数（pageNum, pageSize）

### 返回字段要求
- 基础字段：继承现有`/hostscan/tasksummary/list`接口的所有返回字段
- 新增字段：
  - 任务名称（taskName）- 从sys_job表获取
  - 扫描目标（scanTarget）- 从sys_job表获取
  - 存活主机数量（aliveHostCount）- 统计ffsafe_hostscan_taskresult表记录数
  - 弱口令数量（weakPasswordCount）- 统计ffsafe_hostscan_wpresult表记录数

### 数据库表关联关系
```
主表：ffsafe_scantask_summary
关联表：
- sys_job (通过 job_id 关联，获取任务名称和扫描目标)
- ffsafe_hostscan_taskresult (通过 task_id 关联，统计存活主机数量)
- ffsafe_hostscan_wpresult (通过 task_id 关联，统计弱口令数量)
```

## 技术方案

### 实现策略
采用方案一：扩展现有FfsafeScantaskSummaryController
- 新增`/hostscan/tasksummary/listWithDetails`接口
- 创建新的DTO类包含所有新增字段
- 在Service层新增方法处理复杂查询逻辑
- 使用MyBatis关联查询获取任务名称和统计数据

### 核心技术点
1. **扫描目标解析**：解析sys_job.invoke_target字段提取实际扫描目标
2. **关联查询**：LEFT JOIN多表获取完整数据
3. **统计查询**：使用子查询统计相关表的记录数
4. **分页支持**：使用PageHelper实现分页查询

## 任务拆分

### 任务1：创建查询参数类FfsafeScantaskSummaryQueryParam
- **文件**：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/domain/FfsafeScantaskSummaryQueryParam.java`
- **内容**：包含taskName、scanTarget字段，继承BaseEntity

### 任务2：创建详细返回结果类FfsafeScantaskSummaryDetailVO  
- **文件**：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/domain/FfsafeScantaskSummaryDetailVO.java`
- **内容**：继承原有字段，新增taskName、scanTarget、aliveHostCount、weakPasswordCount

### 任务3：扩展Mapper接口添加新查询方法
- **文件**：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/service/mapper/FfsafeScantaskSummaryMapper.java`
- **内容**：新增selectFfsafeScantaskSummaryDetailList方法

### 任务4：编写复杂关联查询SQL
- **文件**：`aqsoc-monitor/src/main/resources/mapper/ffsafe/FfsafeScantaskSummaryMapper.xml`
- **内容**：LEFT JOIN关联查询，支持条件过滤和分页

### 任务5：创建扫描目标解析工具方法
- **文件**：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/utils/ScanTargetUtils.java`
- **内容**：解析invoke_target字段，提取扫描目标地址

### 任务6：扩展Service接口添加新方法
- **文件**：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/service/IFfsafeScantaskSummaryService.java`
- **内容**：新增方法声明

### 任务7：实现Service业务逻辑
- **文件**：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/service/impl/FfsafeScantaskSummaryServiceImpl.java`
- **内容**：实现业务逻辑，包含扫描目标解析

### 任务8：扩展Controller添加新接口
- **文件**：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/controller/FfsafeScantaskSummaryController.java`
- **内容**：新增listWithDetails接口方法

## 接口规格

### 请求示例
```
GET /hostscan/tasksummary/listWithDetails?pageNum=1&pageSize=10&taskName=测试&scanTarget=192.168
```

### 响应示例
```json
{
  "total": 100,
  "rows": [
    {
      "id": 1,
      "jobId": 217,
      "taskId": 1001,
      "taskType": 1,
      "taskName": "ip漏洞数据非凡",
      "scanTarget": "**************",
      "aliveHostCount": 5,
      "weakPasswordCount": 2,
      "highRiskNum": 3,
      "middleRiskNum": 5,
      "lowRiskNum": 2,
      "startTime": "2024-12-18 07:25:28",
      "endTime": "2024-12-18 08:30:15"
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
```

## 风险控制

1. **invoke_target解析风险**：添加异常处理，解析失败返回空字符串
2. **查询性能风险**：优化SQL，建议添加适当索引
3. **分页准确性**：使用MyBatis分页插件确保正确性

## 验收标准

1. 接口能正确响应请求，返回预期数据格式
2. 支持taskName和scanTarget的模糊查询
3. 正确统计存活主机数量和弱口令数量
4. 分页功能正常工作
5. 扫描目标解析准确
6. 代码符合项目规范，无编译错误
