# 批量报告生成功能开发任务计划

## 项目概述

基于 aqsoc-main 项目中的 FfsafeScantaskSummaryController.createReport 接口，开发一个新的批量报告生成功能。

### 技术架构
- **框架**: 若依框架 + Spring Boot 2.5.14
- **数据库**: MySQL 8
- **ORM**: MyBatis + MyBatis-Plus
- **架构模式**: 三层架构（Controller-Service-Mapper）

### 核心需求
1. **批量报告生成接口** - 支持一次性选择多条扫描任务记录来批量生成报告
2. **数据验证逻辑** - 验证所选记录必须同时满足 `finish_rate=100` 且 `task_status=4`
3. **新建漏扫报告记录表** - 创建 `ffsafe_scan_report_record` 表存储报告记录
4. **数据迁移** - 迁移现有 42 条报表相关数据到新表
5. **第三方接口扩展** - 支持逗号分隔的多个 task_id 传递

## 任务拆分与依赖关系

### 任务 1: 创建漏扫报告记录表和数据迁移
**ID**: `d160cfcd-6f49-4271-b377-859d07db38fa`
**状态**: 待执行
**依赖**: 无
**描述**: 设计并创建 ffsafe_scan_report_record 表，包含完整的字段定义、索引和约束。编写数据迁移SQL脚本。

**关键实现点**:
- 创建包含基础字段和报告相关字段的新表
- 添加必要索引：idx_create_time、idx_report_status
- 从 ffsafe_scantask_summary 和 sys_job 表联查迁移数据
- 使用 mcp-mysql-server 工具执行SQL

### 任务 2: 创建批量报告参数类和实体类
**ID**: `ca9937f5-dac8-4673-bbef-e2087281a6ee`
**状态**: 待执行
**依赖**: 无
**描述**: 创建 BatchCreateReportParam 和 FfsafeScanReportRecord 类。

**关键实现点**:
- BatchCreateReportParam: 包含 Long[] ids 和 Integer taskType
- FfsafeScanReportRecord: 继承 BaseEntity，使用 MyBatis-Plus 注解
- 遵循《阿里代码规约》命名规范

### 任务 3: 创建数据访问层Mapper接口和XML
**ID**: `579875c6-412c-453e-8329-a5bef774d14c`
**状态**: 待执行
**依赖**: 任务2
**描述**: 创建 FfsafeScanReportRecordMapper 接口和 XML 映射文件。

### 任务 4: 创建服务层接口和实现类
**ID**: `8cfd50a0-4fa5-4f73-93c4-2f5af3a35ba2`
**状态**: 待执行
**依赖**: 任务3
**描述**: 创建 IFfsafeScanReportRecordService 接口和实现类。

### 任务 5: 实现批量验证逻辑
**ID**: `b43a3324-bd6e-46eb-a789-c237c8999473`
**状态**: 待执行
**依赖**: 任务4
**描述**: 在 FfsafeScantaskSummaryServiceImpl 中添加批量验证方法。

**关键验证逻辑**:
```java
// 验证条件：finish_rate=100 且 task_status=4
for (FfsafeScantaskSummary task : tasks) {
    if (task.getFinishRate() != 100 || task.getTaskStatus() != 4) {
        throw new ServiceException("任务未完成！");
    }
}
```

### 任务 6: 扩展第三方接口调用支持批量
**ID**: `b7c227f5-7f03-4bae-bde9-25724757fb01`
**状态**: 待执行
**依赖**: 任务5
**描述**: 修改 CreateTaskReportParam 类支持逗号分隔的多个 task_id。

### 任务 7: 实现批量报告生成核心逻辑
**ID**: `96b6a278-f9da-485d-aca7-e1f6a9c3a0e8`
**状态**: 待执行
**依赖**: 任务6
**描述**: 在 ScanTaskReport 类中添加批量报告生成方法。

**核心流程**:
1. 批量验证任务状态
2. 构建批量 task_id 字符串（逗号分隔）
3. 调用第三方接口
4. 插入新表记录
5. 添加到监控事件

### 任务 8: 添加批量报告生成控制器接口
**ID**: `7b414049-7285-4258-b17f-4171bbe8bcdc`
**状态**: 待执行
**依赖**: 任务7
**描述**: 在 FfsafeScantaskSummaryController 中添加 /createbatchreport 接口。

**接口设计**:
- 路径: `/createbatchreport`
- 方法: POST
- 参数: BatchCreateReportParam
- 返回: AjaxResult

### 任务 9: 完善报告状态更新和监控机制
**ID**: `d8195abb-913c-45d4-ae63-5f4cba2ab041`
**状态**: 待执行
**依赖**: 任务8
**描述**: 修改 TaskReportMonitorEvent 支持新表状态监控。

## 关键技术决策

### 1. 数据迁移策略
- **保守迁移**: 保留原表字段，新功能使用新表
- **向后兼容**: 现有功能不受影响
- **渐进式**: 可逐步迁移，降低风险

### 2. 批量处理优化
- **批量查询**: 使用现有 selectFfsafeScantaskSummaryByIds 方法
- **事务管理**: 使用 @Transactional 确保数据一致性
- **异常处理**: 统一使用 ServiceException 和 AjaxResult

### 3. 第三方接口兼容
- **向后兼容**: 保持原有单个 task_id 调用方式
- **扩展支持**: 新增逗号分隔的多个 task_id 支持
- **参数重载**: 提供多个 parseParam 方法

## 验收标准

### 功能验收
1. 批量选择多条任务记录生成报告
2. 验证逻辑正确（finish_rate=100 且 task_status=4）
3. 第三方接口支持逗号分隔的多个 task_id
4. 新表正确记录批量报告信息
5. 报告状态正确更新和监控

### 技术验收
1. 遵循《阿里代码规约》
2. 数据库事务一致性
3. 异常处理完善
4. 日志记录详细
5. 向后兼容性保证

## 风险控制

### 数据风险
- 数据迁移前备份
- 分步执行，可回滚
- 验证数据完整性

### 功能风险
- 保留原表字段
- 新功能独立实现
- 充分测试验证

### 性能风险
- 批量查询优化
- 分批处理机制
- 索引优化支持

## 执行计划

**预计总工期**: 5-7 个工作日
**关键路径**: 任务1 → 任务2 → 任务3 → 任务4 → 任务5 → 任务6 → 任务7 → 任务8 → 任务9

**并行执行机会**:
- 任务1（数据库）和任务2（实体类）可并行
- 任务完成后进行集成测试

---

*本计划遵循《阿里代码规约》和项目既有架构模式，确保功能完整性和系统稳定性。*
