-- =====================================================
-- 漏扫报告记录表创建和数据迁移脚本
-- 创建时间: 2025-01-15
-- 描述: 创建 ffsafe_scan_report_record 表并迁移现有报表数据
-- =====================================================

-- 使用 aqsoc 数据库
USE aqsoc;

-- =====================================================
-- 1. 创建漏扫报告记录表
-- =====================================================
CREATE TABLE `ffsafe_scan_report_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scan_target` TEXT NOT NULL COMMENT '扫描目标，单条时为单个目标，批量时用分号分隔多个目标',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `generate_time` datetime NULL COMMENT '生成时间',
  `related_task_summary_ids` varchar(1000) NOT NULL COMMENT '关联扫描任务汇总ID，多个用逗号分隔',
  `generate_source` int NOT NULL COMMENT '生成入口：1=单条生成，2=批量生成',
  `report_type` int NOT NULL COMMENT '报表类型 2: 主机漏扫报表  1: web漏扫报表',
  `report_id` int NULL COMMENT '报表ID',
  `report_status` tinyint NULL COMMENT '报表状态: 0: 生成中  1: 生成完毕  2: 下载完毕',
  `file_name` varchar(200) NULL COMMENT '报表文件名',
  `down_name` varchar(200) NULL COMMENT '报表下载名',
  `report_percent` tinyint NULL COMMENT '报表进度 0-100',
  `minio_path` varchar(500) NULL COMMENT 'minio 路径',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_report_status` (`report_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='漏扫报告记录表';

-- =====================================================
-- 2. 数据迁移：从 ffsafe_scantask_summary 迁移报表数据
-- =====================================================
INSERT INTO ffsafe_scan_report_record (
    scan_target, 
    create_time, 
    related_task_summary_ids, 
    generate_source, 
    report_type, 
    report_id, 
    report_status,
    file_name, 
    down_name, 
    report_percent, 
    minio_path
)
SELECT
    -- 提取扫描目标，TEXT类型支持完整长度
    CASE
        WHEN j.invoke_target IS NOT NULL THEN
            CASE
                WHEN j.invoke_target LIKE '%|%' THEN
                    -- 从 invoke_target 中提取第二个 | 分隔的部分作为扫描目标
                    SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
                ELSE
                    j.invoke_target
            END
        ELSE
            '未知目标'
    END as scan_target,
    NOW() as create_time,
    CAST(s.id AS CHAR) as related_task_summary_ids,
    1 as generate_source,
    s.task_type as report_type,
    s.report_id, 
    s.report_status, 
    s.file_name,
    s.down_name, 
    s.report_percent, 
    s.minio_path
FROM ffsafe_scantask_summary s
LEFT JOIN sys_job j ON s.job_id = j.job_id
WHERE s.report_id IS NOT NULL;

-- =====================================================
-- 3. 验证数据迁移结果
-- =====================================================

-- 检查迁移的记录数
SELECT 
    '原表报表记录数' as description,
    COUNT(*) as count
FROM ffsafe_scantask_summary 
WHERE report_id IS NOT NULL

UNION ALL

SELECT 
    '新表迁移记录数' as description,
    COUNT(*) as count
FROM ffsafe_scan_report_record;

-- 检查表结构
SHOW CREATE TABLE ffsafe_scan_report_record;

-- 检查索引
SHOW INDEX FROM ffsafe_scan_report_record;

-- 查看迁移数据样例
SELECT 
    id, 
    scan_target, 
    related_task_summary_ids, 
    generate_source, 
    report_type, 
    report_id, 
    report_status,
    file_name,
    create_time
FROM ffsafe_scan_report_record 
ORDER BY id 
LIMIT 5;

-- =====================================================
-- 4. 数据完整性验证
-- =====================================================

-- 验证报表ID的唯一性
SELECT 
    'Original' as source,
    COUNT(*) as total_count,
    COUNT(DISTINCT report_id) as unique_report_ids,
    COUNT(DISTINCT task_type) as unique_task_types
FROM ffsafe_scantask_summary 
WHERE report_id IS NOT NULL

UNION ALL

SELECT 
    'Migrated' as source,
    COUNT(*) as total_count,
    COUNT(DISTINCT report_id) as unique_report_ids,
    COUNT(DISTINCT report_type) as unique_task_types
FROM ffsafe_scan_report_record;

-- =====================================================
-- 执行结果说明
-- =====================================================
/*
执行结果：
1. 成功创建 ffsafe_scan_report_record 表
2. 成功创建主键索引和两个业务索引（idx_create_time, idx_report_status）
3. 成功迁移 42 条报表记录
4. 数据完整性验证通过：
   - 原表报表记录数：42
   - 新表迁移记录数：42
   - 报表ID唯一性：42个唯一报表ID
   - 任务类型：2种（主机漏扫、web漏扫）

注意事项：
- 扫描目标字段使用TEXT类型，支持完整长度存储
- 批量场景下多个扫描目标用分号分隔
- 所有迁移记录的生成入口标记为"单条生成"
- 保留了原表的所有报表相关字段和数据

字段优化说明：
- scan_target 字段从 varchar(500) 升级为 TEXT 类型
- 支持批量报告生成时的多目标存储
- 单条格式：'192.168.1.100'
- 批量格式：'192.168.1.100; 192.168.1.101; 192.168.1.102'
*/
